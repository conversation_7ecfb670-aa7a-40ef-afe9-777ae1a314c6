'use client'
import React, { useEffect, useRef, useState } from 'react'
import { useForm, SubmitHandler } from "react-hook-form"
import Desc from './form/desc'
import Style from './form/style'
import Hd from './form/hd'
import Size from './form/size'
import { useTranslation } from 'react-i18next'
import { useConfig } from '@share/src/configContext'
import useRequest from '@share/src/hook/useRequest'
import cn from 'classnames'
import { RotateCw } from 'lucide-react'
import Image from 'next/image'
import useDialogAlert from '@share/src/hook/useDialogAlert'
import { Heart, ThumbsDown } from 'lucide-react';
import Link from 'next/link'
import Download from './download'

let itv: any = null;
let timeoutHandler: any = null;
const Gen = () => {
    const { t } = useTranslation()
    const config = useConfig()
    const [loading, setLoading] = useState(false)
    const [imgInfo, setImgInfo] = useState<any>(null)
    const dialogAlert = useDialogAlert();
    const [like, setLike] = useState(false)
    const [disLike, setDisLike] = useState(false)
    const [recordId, setRecordId] = useState('')
    const scrollRef = useRef<HTMLDivElement>(null)

    const {
        register,
        handleSubmit,
        watch,
        formState: { errors },
    } = useForm<any>({ mode: 'onChange', defaultValues: { image_quality: config?.image_quality_list?.[1].key, image_aspect_ratio: config?.image_aspect_ratio_list?.[1].key, image_style: config?.image_style_list?.[0].key } })
    const request = useRequest();
    const formValues = watch();
    const showErr = (msg?: string) => {
        dialogAlert.show({
            title: t('app.img.gen_failed'),
            desc: msg || t('app.img.gen_failed_desc'),
            alertStatus: -1
        })
    }
    const onSubmit: SubmitHandler<any> = async (data) => {
        if (loading) return;
        // console.log(data);
        setLoading(true)
        try {
            const res = await request('/image/generate/submit', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify(data)
            })
            // console.log('res', res);
            if (res.error_code === 0) {
                setRecordId(res.data.record_id)
                // 每两秒轮询，持续6次，超过展示超时，成功展示成功
                itv = setInterval(async () => {
                    const imgRes = await request(`/image/generate/check?record_id=${res.data.record_id}`)
                    // console.log('imgRes', imgRes);
                    if (imgRes.error_code === 0 && imgRes.data.status === 'SUCCESS') {
                        clearInterval(itv)
                        clearTimeout(timeoutHandler)
                        setLoading(false)
                        setImgInfo(imgRes.data)
                    } else if (imgRes.data.status === 'FAILED') {
                        clearInterval(itv)
                        clearTimeout(timeoutHandler)
                        setLoading(false)
                        showErr()
                    }
                }, 2000)
                timeoutHandler = setTimeout(() => {
                    clearInterval(itv)
                    setLoading(false)
                    dialogAlert.show({
                        title: t('app.img.timeout'),
                        desc: t('app.img.timeout_desc'),
                        alertStatus: '2'
                    })
                }, 12000)
            } else {
                setLoading(false)
                showErr(res?.message)
            }
        } catch (error) {
            showErr()
            setLoading(false)
        }
    }
    useEffect(() => {
        return () => {
            clearInterval(itv)
            clearTimeout(timeoutHandler)
        }
    }, [])
    const likeHandler = async (like_status: number) => {
        const res = await request(`/image/like_status`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({ record_id: recordId, like_status })
        })
        if(res.error_code === 0) {
            if(like_status === 1) {
                setLike(true)
                setDisLike(false)
            } else {
                setDisLike(true)
                setLike(false)
            }
        }
    }
    useEffect(() => {
        if(scrollRef.current) {
            scrollRef.current.scrollTo({ top: 9999, behavior: 'smooth' })
        }
    }, [loading])
    return (
        <div ref={scrollRef} className='px-2 h-[calc(100vh_-_150px_-_var(--bottom-margin))] overflow-y-auto'>
            <form onSubmit={handleSubmit(onSubmit)} className=''>
                <Desc register={register} formValues={formValues} errors={errors} />
                <Style register={register} errors={errors} />
                <Hd register={register} errors={errors} />
                <Size register={register} errors={errors} />
                {loading ? <div className='rounded flex items-center justify-center py-2 dark:bg-gray-800 bg-gray-200 my-2 h-36'><RotateCw className="w-5 h-5 mr-1 animate-spin" />{t('app.img.img_task_info')}</div> : <>{imgInfo && <div className='relative rounded my-2'>
                    <div className='absolute text-xs right-1 top-1 px-2 py-1 bg-gray-800/50 rounded'>{imgInfo.image_width}x{imgInfo.image_height}</div>
                    <Image src={imgInfo.image_url} alt="" width={imgInfo.image_width} height={imgInfo.image_height} className='w-full h-auto' unoptimized={true} />
                    <div className='flex justify-end mt-2'>
                        <Download url={imgInfo.image_url} className='rounded-full px-3 py-1.5 bg-white dark:bg-gray-800 text-xs flex justify-center items-center mr-2 dark:text-white text-gray-600' />
                        <button type='button' onClick={() => likeHandler(1)} className={cn('rounded-full px-3 py-1.5 bg-white dark:bg-gray-800 text-xs flex justify-center items-center mr-2 dark:text-white text-gray-600', like && '!text-pink-500')}>
                            <Heart className={cn('w-4 h-4 mr-0.5')} />
                        </button>
                        <button type='button' onClick={() => likeHandler(2)} className={cn('rounded-full px-3 py-1.5 bg-white dark:bg-gray-800 text-xs flex justify-center items-center mr-2 dark:text-white text-gray-600', disLike && '!text-amber-500')}>
                            <ThumbsDown className='w-4 h-4 mr-0.5' />
                        </button>
                    </div>
                </div>}</>}
                <button type='submit' className={cn('fixed bottom-2 w-[calc(100%_-_16px)] left-2 right-2 p-2 px-5 bg-purple-500 text-white rounded flex items-center justify-center', loading && 'opacity-50 cursor-not-allowed')}>
                    {loading ? t('app.img.loading') : t('app.img.submit_btn')}
                </button>
            </form>
        </div>
    )
}

export default Gen
