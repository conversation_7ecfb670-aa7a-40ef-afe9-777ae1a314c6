'use client'
import React, { useCallback, useContext, useEffect, useLayoutEffect, useRef, useState } from 'react'
import cn from 'classnames'
import { usePara<PERSON>, useRouter } from 'next/navigation'
import s from '@/app/[lang]/globals.module.css'
import { AuthContext } from '@little-tavern/shared/src/authContext'
import useRequest from '@little-tavern/shared/src/hook/useRequest'
import useDialogAlert from '@little-tavern/shared/src/hook/useDialogAlert'
import Toast from '@little-tavern/shared/src/ui/toast'
import DialogUSDT from './DialogUSDT'
import Share from '@/app/[lang]/components/share'
import PayMethods from './payMethods'
import Inventory from '@/app/[lang]/components/inventory'
import { LoadingToast } from '@little-tavern/shared/src/ui/Loading'
import { web, tgWeb, customerBot } from '@/app/module/global'
import { Trans, useTranslation } from 'react-i18next'
import Link from 'next/link'
import { useBackButton } from '@share/src/hooks/useBackButton'
import TelegramWebView from '@share/src/module/sdk'

const btn = cn(s.primBtn, 'mx-2 text-sm')

const Pay = ({ products }: any) => {
  const { t } = useTranslation()
  const params = useParams()
  const lang = params.lang as string
  const [payedBanlance, setPayedBanlance] = useState<number>(0)
  const [rewardBanlance, setRewardBanlance] = useState<number>(0)
  const [USDTBalance, setUSDTBalance] = useState(0)
  const [inventorys, setInventorys] = useState<any>(products)
  const [rechargeData, setRechargeData] = useState<any>(null)
  const auth = useContext(AuthContext);
  const isLogin = auth?.isLogin;
  const request = useRequest();
  const dialogAlert = useDialogAlert();
  const [showExchangeUSDT, setShowExchangeUSDT] = useState(false)
  const [selectedInventoryIndex, setSelectedInventoryIndex] = useState<number>(0)
  const [loading, setIsLoading] = useState(true);
  const { backHandler } = useBackButton();

  const fetchBanlance = async () => {

    const res = await request('/recharge/balance');
    setIsLoading(false)
    setPayedBanlance(res.payed_balance);
    setRewardBanlance(res.reward_balance)
    setUSDTBalance(res.fiat_balance)
  }

  useEffect(() => {
    const searchParam = new URLSearchParams(location.search)
    // s=e，显示卡密兑换弹窗
    if (searchParam.get('s') == 'e') {
      setShowExchangeUSDT(true)
    }
  }, [])

  useEffect(() => {
    isLogin && fetchBanlance();
  }, [isLogin])
  useEffect(() => {
    const fetchInventorys = async () => {
      const target_product_id = new URLSearchParams(location.search).get('target_product_id')
      const res = await request(`/recharge/list/v2${target_product_id ? `?target_product_id=${target_product_id}` : ''}`);
      setInventorys(res.products);
      setRechargeData(res)
      // 选择开头不是disabled的套餐
      const selectIndex = res.products.findIndex((item: any) => !item.disabled)
      setSelectedInventoryIndex(selectIndex === -1 ? 0 : selectIndex)
    }
    if (inventorys === undefined || inventorys?.length == 0) {
      fetchInventorys();
    }
    const payStatus = new URLSearchParams(location.search).get('paystatus')
    if (payStatus == '1') {
      dialogAlert.show({
        title: t('app.pay.charge_success'),
        desc: t('app.pay.charge_success_desc')
      });
    } else if (payStatus == '0') {
      dialogAlert.show({
        title: t('app.pay.charge_failed'),
        desc: t('app.pay.charge_failed_desc'),
        status: '0'
      });
    }
  }, [])

  // 卡密兑换弹窗
  const rechargeUSDT = () => {
    setShowExchangeUSDT(true)
  }
  const onCloseDialogUSDT = (res: any) => {
    console.log('res', res);
    setShowExchangeUSDT(false)
    if (res === undefined) {
      return;
    }
    if (res.success) {
      Toast.notify({ type: 'success', message: t('app.pay.charge_success') })
      setPayedBanlance(res.payed_balance);
      setRewardBanlance(res.reward_balance);
    } else {
      Toast.notify({ type: 'error', message: t('app.pay.charge_err') + res.msg })
    }
  }

  const rechargeGuide = async () => {
    const res = await dialogAlert.show({
      title: t('app.pay.recharge_guide_title'),
      desc: tgWeb ? t('app.pay.recharge_guide_desc_tgweb') : t('app.pay.recharge_guide_desc'),
      alertStatus: 2
    })
  }
  const pageUrl = 'http://localhost:3000/zh/pay#reload=1'
  return (
    <>
      <main className="main-height w-full px-2 !transform-none">
        {inventorys?.length > 0 && <div className="con-width relative pb-5">
          <Share className='' />
          <div className='pt-2.5 pb-1'>
            <span className='text-sm'>{t('app.pay.remain_diamond')}</span><button onClick={() => {
              dialogAlert.show({
                title: t('app.pay.recharge_explain'),
                desc: t('app.pay.diamond_intro_desc'),
                alertStatus: 2
              })
            }} className='ml-1 text-center text-gray-500 hover:text-white border-gray-500 hover:border-white rounded-full border w-4 h-4 text-xs'>?</button>
            <span className='ml-2 text-sm'>{t('app.pay.diamond_intro')}</span>
          </div>
          <div className='border dark:border-gray-500  max-w-lg px-4 py-2 rounded-md text-sm leading-7 dark:bg-gray-900 bg-white'>
            <div className='flex justify-between'>
              <h2 className='text-base'>
                <span className='mr-2'>{payedBanlance} 💎 </span>
                <span>{rewardBanlance} 🟡 </span>
              </h2>
              {!web && <button onClick={rechargeUSDT} className={cn(btn, 'text-xs !px-3')}>{t('app.pay.code_exchange')}</button>}
            </div>
            <div className='text-xs mt-1.5'>
              <Trans i18nKey="app.pay.recharge_explain1" t={t}>
                充值完成后一般5分钟内💎都会到账。<Link href={customerBot} target='_blank' className='underline text-blue-500'>联系客服</Link>
              </Trans>
            </div>
          </div>
          <div className='pt-1 pb-0.5'>
            <span className='mr-2 text-sm'>{t('app.pay.inventory')}</span>
            <button onClick={rechargeGuide} className='underline text-sm text-blue-500'>{t('app.pay.recharge_btn')}</button>
          </div>
          <Inventory inventorys={inventorys} rechargeData={rechargeData} selectedInventoryIndex={selectedInventoryIndex} select={setSelectedInventoryIndex} />
          {/* <div className='mt-3 border dark:border-gray-500  max-w-lg px-4 py-3 rounded-md text-sm leading-7 dark:bg-gray-900 bg-white'>
            <h3 className='text-sm'>{t('app.pay.pay_tips')}</h3>
            <div className='text-sm'>{t('app.pay.pay_tips_desc')}</div>
            <div className="mt-2 flex items-center gap-1">
              <p className='rounded-full dark:bg-gray-800 bg-gray-200 dark:text-gray-400 text-gray-500 px-2 py-1 text-xs truncate w-60'>{pageUrl}</p>
              <button onClick={() => {
                navigator.clipboard.writeText(pageUrl).then(() => {
                  Toast.notify({ type: 'success', message: t('app.pay.copyed_success') })
                }).catch(err => {
                  console.error(t('app.pay.copy_fail'), err);
                });
              }} className='px-2 py-1 bg-purple-500 text-white rounded ml-1 text-xs'>{t('app.pay.copy')}</button>
            </div>
          </div> */}
          {!tgWeb && <div className='mt-3 border dark:border-gray-500  max-w-lg px-4 py-3 rounded-md text-sm leading-7 dark:bg-gray-900 bg-white'>
            <h3 className='text-sm'>{t('app.pay.contact')}</h3>
            <div className='flex gap-2 mt-2'>
              <button onClick={() => {
                TelegramWebView.openTelegramLink('https://t.me/playai666', {})
              }} type='button' className='px-2 py-1 bg-purple-500 text-white rounded text-sm'>{t('app.pay.tg')}</button>
              <button onClick={() => {
                TelegramWebView.openTelegramLink('https://t.me/ai_x01_bot', {})
              }} type='button' className='px-2 py-1 bg-purple-500 text-white rounded text-sm'>{t('app.pay.service')}</button>
            </div>
          </div>}
          <div className='h-48'></div>
          <PayMethods chargeSuccess={() => { fetchBanlance() }} USDTBalance={USDTBalance} supported_pay_types={inventorys?.[selectedInventoryIndex]?.supported_pay_types} recharge_id={inventorys?.[selectedInventoryIndex]?.recharge_product_id}></PayMethods>
          <DialogUSDT isOpen={showExchangeUSDT} onClose={onCloseDialogUSDT} />
        </div>}
        {loading && <LoadingToast msg={t('app.common.loading')} />}
      </main>
    </>
  )
}

export default React.memo(Pay)
