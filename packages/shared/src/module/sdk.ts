// WebView - Simplified SDK in TypeScript

// Define types
interface InitParams {
  [key: string]: any;
  tgWebAppPlatform?: string;
}

// Define WebViewEventCallback type and BackButtonInterface before using them
type WebViewEventCallback = (eventType: string, ...args: any[]) => void;

// BackButton interface definition
interface BackButtonInterface {
  isVisible: boolean;
  onClick(callback: WebViewEventCallback): BackButtonInterface;
  offClick(callback: WebViewEventCallback): BackButtonInterface;
  show(): BackButtonInterface;
  hide(): BackButtonInterface;
}

interface TelegramWebView {
  initParams: InitParams;
  initData: string;
  platform: string;
  expand: () => void;
  enableClosingConfirmation: () => void;
  disableVerticalSwipes: () => void;
  requestFullscreen: () => void;
  BackButton: BackButtonInterface;
  setBackgroundColor: (color: string) => void
  downloadFile: (params: any) => void
  openLink: (url: string, options: any) => void
  openTelegramLink: (url: string, options: any) => void
}

// Create and export the WebView module
const createTelegramWebView = (): TelegramWebView => {
  let webAppVersion = '6.0';
  // Parse URL parameters to get initData
  let locationHash = '';
  if (typeof window !== 'undefined') {
    if (!window.Telegram) {
      window.Telegram = {};
    }
    window.Telegram.WebView = {
      onEvent: onEvent,
      offEvent: offEvent,
      postEvent: postEvent,
      receiveEvent: receiveEvent,
      callEventCallbacks: callEventCallbacks
    };

    window.Telegram.Utils = {
      urlSafeDecode: urlSafeDecode,
      urlParseQueryString: urlParseQueryString,
      urlParseHashParams: urlParseHashParams,
    };

    // App backward compatibility
    window.TelegramGameProxy = {
      receiveEvent: receiveEvent
    };
  }

  try {
    locationHash = location.hash.toString();
  } catch (e) {
    // Ignore error
  }

  function receiveEvent(eventType: string, eventData: any) {
    console.log('[Telegram.WebView1] < receiveEvent', eventType, eventData);
    callEventCallbacks(eventType, (callback) => {
      callback(eventType, eventData);
    });
  }

  var webAppDownloadFileRequested: any = false;
  function onFileDownloadRequested(eventType: string, eventData: any) {
    if (webAppDownloadFileRequested) {
      var requestData = webAppDownloadFileRequested;
      webAppDownloadFileRequested = false;
      var isDownloading = eventData.status == 'downloading';
      if (requestData.callback) {
        requestData.callback(isDownloading);
      }
      receiveWebViewEvent('fileDownloadRequested', {
        status: isDownloading ? 'downloading' : 'cancelled'
      });
    }
  }

  let isIframe = false;
  let iFrameStyle: HTMLStyleElement | null = null;

  try {
    isIframe = (typeof window !== 'undefined' && window.parent != null && window !== window.parent);

    if (isIframe) {
      window.addEventListener('message', (event: MessageEvent) => {
        if (event.source !== window.parent) return;

        try {
          const dataParsed = JSON.parse(event.data as string);

          if (!dataParsed || !dataParsed.eventType) {
            return;
          }

          if (dataParsed.eventType === 'set_custom_style') {
            if (event.origin === 'https://web.telegram.org' && iFrameStyle) {
              iFrameStyle.innerHTML = dataParsed.eventData;
            }
          } else if (dataParsed.eventType === 'reload_iframe') {
            try {
              window.parent.postMessage(JSON.stringify({ eventType: 'iframe_will_reload' }), '*');
            } catch (e) {
              // Ignore error
            }
            location.reload();
          } else {
            receiveEvent(dataParsed.eventType, dataParsed.eventData);
          }
        } catch (e) {
          return;
        }
      });

      iFrameStyle = document.createElement('style');
      document.head.appendChild(iFrameStyle);

      try {
        window.parent.postMessage(JSON.stringify({
          eventType: 'iframe_ready',
          eventData: { reload_supported: true }
        }), '*');
      } catch (e) {
        // Ignore error
      }
    }
  } catch (e) {
    // Ignore error
  }

  const initParams = urlParseHashParams(locationHash);

  // Generate initData string from hash parameters
  let initDataString = '';
  if (initParams.tgWebAppData) {
    initDataString = initParams.tgWebAppData;
  }
  if (initParams.tgWebAppVersion) {
    webAppVersion = initParams.tgWebAppVersion;
  }

  // URL parsing utilities
  function urlSafeDecode(urlencoded: string): string {
    try {
      urlencoded = urlencoded.replace(/\+/g, '%20');
      return decodeURIComponent(urlencoded);
    } catch (e) {
      return urlencoded;
    }
  }

  function urlParseHashParams(locationHash: string): InitParams {
    locationHash = locationHash.replace(/^#/, '');
    const params: InitParams = {};
    if (!locationHash.length) {
      return params;
    }
    if (locationHash.indexOf('=') < 0 && locationHash.indexOf('?') < 0) {
      params._path = urlSafeDecode(locationHash);
      return params;
    }
    const qIndex = locationHash.indexOf('?');
    if (qIndex >= 0) {
      const pathParam = locationHash.substr(0, qIndex);
      params._path = urlSafeDecode(pathParam);
      locationHash = locationHash.substr(qIndex + 1);
    }
    const query_params = urlParseQueryString(locationHash);
    for (const k in query_params) {
      params[k] = query_params[k];
    }
    return params;
  }

  function urlParseQueryString(queryString: string): Record<string, string | null> {
    const params: Record<string, string | null> = {};
    if (!queryString.length) {
      return params;
    }
    const queryStringParams = queryString.split('&');
    let param, paramName, paramValue;
    for (let i = 0; i < queryStringParams.length; i++) {
      param = queryStringParams[i].split('=');
      paramName = urlSafeDecode(param[0]);
      paramValue = param[1] == null ? null : urlSafeDecode(param[1]);
      params[paramName] = paramValue;
    }
    return params;
  }

  // Event posting utility
  function postEvent(eventType: string, eventData?: any): void {
    if (eventData === undefined) {
      eventData = '';
    }

    if ((window as any).TelegramWebviewProxy !== undefined) {
      (window as any).TelegramWebviewProxy.postEvent(eventType, JSON.stringify(eventData));
    }
    else if ((window as any).external && 'notify' in (window as any).external) {
      (window as any).external.notify(JSON.stringify({ eventType, eventData }));
    } else if (isIframe) {
      try {
        var trustedTarget = 'https://web.telegram.org';
        // For now we don't restrict target, for testing purposes
        trustedTarget = '*';
        window.parent.postMessage(JSON.stringify({ eventType: eventType, eventData: eventData }), trustedTarget);
      } catch (e) {
      }
    }
    else {
    }
  }

  function versionCompare(v1: any, v2: any): number {
    if (typeof v1 !== 'string') v1 = '';
    if (typeof v2 !== 'string') v2 = '';
    v1 = v1.replace(/^\s+|\s+$/g, '').split('.');
    v2 = v2.replace(/^\s+|\s+$/g, '').split('.');
    const a = Math.max(v1.length, v2.length);
    for (let i = 0; i < a; i++) {
      const p1 = parseInt(v1[i]) || 0;
      const p2 = parseInt(v2[i]) || 0;
      if (p1 == p2) continue;
      if (p1 > p2) return 1;
      return -1;
    }
    return 0;
  }

  function versionAtLeast(ver: any) {
    return versionCompare(webAppVersion, ver) >= 0;
  }

  // Event handling functions for WebView events
  const eventHandlers: Record<string, WebViewEventCallback[]> = {};

  function onEvent(eventType: string, callback: WebViewEventCallback): void {
    if (eventHandlers[eventType] === undefined) {
      eventHandlers[eventType] = [];
    }
    const index = eventHandlers[eventType].indexOf(callback);
    if (index === -1) {
      eventHandlers[eventType].push(callback);
    }
  }

  function offEvent(eventType: string, callback: WebViewEventCallback): void {
    if (eventHandlers[eventType] === undefined) {
      return;
    }
    const index = eventHandlers[eventType].indexOf(callback);
    if (index === -1) {
      return;
    }
    eventHandlers[eventType].splice(index, 1);
  }

  function callEventCallbacks(eventType: string, func: (callback: WebViewEventCallback) => void): void {
    const curEventHandlers = eventHandlers[eventType];
    if (curEventHandlers === undefined || !curEventHandlers.length) {
      return;
    }
    for (let i = 0; i < curEventHandlers.length; i++) {
      try {
        func(curEventHandlers[i]);
      } catch (e) {
        // Ignore errors in callbacks
      }
    }
  }

  function receiveWebViewEvent(eventType: string, ...args: any[]): void {
    callEventCallbacks('webview:' + eventType, (callback) => {
      callback(eventType, ...args);
    });
  }

  function onWebViewEvent(eventType: string, callback: WebViewEventCallback): void {
    onEvent('webview:' + eventType, callback);
  }

  function offWebViewEvent(eventType: string, callback: WebViewEventCallback): void {
    offEvent('webview:' + eventType, callback);
  }

  // BackButton implementation
  const BackButton = ((): BackButtonInterface => {
    let isVisible = false;
    let curButtonState: string | null = null;

    // Create backButton object with TypeScript interface
    const backButton = {} as BackButtonInterface;

    // Define isVisible property with getter and setter
    Object.defineProperty(backButton, 'isVisible', {
      set: function (val: boolean) { setParams({ is_visible: val }); },
      get: function (): boolean { return isVisible; },
      enumerable: true
    });
    function onBackButtonPressed() {
      receiveWebViewEvent('backButtonClicked');
    }
    onEvent('back_button_pressed', onBackButtonPressed);
    // Helper function to get button parameters
    function buttonParams(): { is_visible: boolean } {
      return { is_visible: isVisible };
    }

    // Helper function to get button state as JSON string
    function buttonState(btn_params?: { is_visible: boolean }): string {
      if (typeof btn_params === 'undefined') {
        btn_params = buttonParams();
      }
      return JSON.stringify(btn_params);
    }

    // Helper function to check if the version supports back button
    function buttonCheckVersion(): boolean {
      if (!versionAtLeast('6.1')) {
        console.warn('[Telegram.WebApp] BackButton is not supported in version ' + webAppVersion);
        return false;
      }
      return true;
    }

    // Helper function to update button state
    function updateButton(): void {
      const btn_params = buttonParams();
      const btn_state = buttonState(btn_params);
      if (curButtonState === btn_state) {
        return;
      }
      curButtonState = btn_state;
      postEvent('web_app_setup_back_button', btn_params);
    }

    // Helper function to set button parameters
    function setParams(params: { is_visible?: boolean }): BackButtonInterface {
      if (!buttonCheckVersion()) {
        return backButton;
      }
      if (typeof params.is_visible !== 'undefined') {
        isVisible = !!params.is_visible;
      }
      updateButton();
      return backButton;
    }

    // Public methods
    backButton.onClick = function (callback: WebViewEventCallback): BackButtonInterface {
      if (buttonCheckVersion()) {
        onWebViewEvent('backButtonClicked', callback);
      }
      return backButton;
    };

    backButton.offClick = function (callback: WebViewEventCallback): BackButtonInterface {
      if (buttonCheckVersion()) {
        offWebViewEvent('backButtonClicked', callback);
      }
      return backButton;
    };

    backButton.show = function (): BackButtonInterface {
      return setParams({ is_visible: true });
    };

    backButton.hide = function (): BackButtonInterface {
      return setParams({ is_visible: false });
    };

    return backButton;
  })();

  // Create WebView object with only required methods
  const telegramWebView: TelegramWebView = {
    // Core data fields
    initParams,
    initData: initDataString,
    platform: initParams.tgWebAppPlatform || 'unknown',

    // Required methods
    expand: function () {
      postEvent('web_app_expand');
    },

    enableClosingConfirmation: function () {
      postEvent('web_app_setup_closing_behavior', { need_confirmation: true });
    },

    disableVerticalSwipes: function () {
      postEvent('web_app_setup_swipe_behavior', { allow_vertical_swipe: false });
    },

    requestFullscreen: function () {
      if (!versionAtLeast('8.0')) {
        console.error('[Telegram.WebApp] Method requestFullscreen is not supported in version ' + webAppVersion);
        throw Error('WebAppMethodUnsupported');
      }
      postEvent('web_app_request_fullscreen');
    },

    // Add BackButton to the WebView object
    BackButton,
    setBackgroundColor: function (color: string) {
      postEvent('web_app_set_background_color', { color: color });
    },
    downloadFile: function (params) {
      if (!versionAtLeast('8.0')) {
        window.open(params.url, '_blank');
        console.error('[Telegram.WebApp] Method downloadFile is not supported in version ' + webAppVersion);
        throw Error('WebAppMethodUnsupported');
      }
      if (webAppDownloadFileRequested) {
        console.error('[Telegram.WebApp] Popup is already opened');
        throw Error('WebAppDownloadFilePopupOpened');
      }
      var a: any = document.createElement('A');

      var dl_params: any = {};
      if (!params || !params.url || !params.url.length) {
        console.error('[Telegram.WebApp] Url is required');
        throw Error('WebAppDownloadFileParamInvalid');
      }
      a.href = params.url;
      if (a.protocol != 'https:') {
        console.error('[Telegram.WebApp] Url protocol is not supported', params.url);
        throw Error('WebAppDownloadFileParamInvalid');
      }
      dl_params.url = a.href;

      if (!params || !params.file_name || !params.file_name.length) {
        console.error('[Telegram.WebApp] File name is required');
        throw Error('WebAppDownloadFileParamInvalid');
      }
      dl_params.file_name = params.file_name;

      postEvent('web_app_request_file_download', dl_params);
    },
    openLink: (url: string, options: any) => {
      var a: any = document.createElement('A');
      a.href = url;
      if (a.protocol != 'http:' &&
        a.protocol != 'https:') {
        console.error('[Telegram.WebApp] Url protocol is not supported', url);
        throw Error('WebAppTgUrlInvalid');
      }
      var url: string = a.href;
      options = options || {};
      if (versionAtLeast('6.1')) {
        var req_params: any = { url: url };
        if (versionAtLeast('6.4') && options.try_instant_view) {
          req_params.try_instant_view = true;
        }
        if (versionAtLeast('7.6') && options.try_browser) {
          req_params.try_browser = options.try_browser;
        }
        postEvent('web_app_open_link', req_params);
      } else {
        window.open(url, '_blank');
      }
    },
    openTelegramLink: (url: string, options: any) => {
      var a: any = document.createElement('A');
      a.href = url;
      if (a.protocol != 'http:' &&
        a.protocol != 'https:') {
        console.error('[Telegram.WebApp] Url protocol is not supported', url);
        throw Error('WebAppTgUrlInvalid');
      }
      if (a.hostname != 't.me') {
        console.error('[Telegram.WebApp] Url host is not supported', url);
        throw Error('WebAppTgUrlInvalid');
      }
      var path_full = a.pathname + a.search;
      options = options || {};
      if (isIframe || versionAtLeast('6.1')) {
        var req_params: any = { path_full: path_full };
        if (options.force_request) {
          req_params.force_request = true;
        }
        postEvent('web_app_open_tg_link', req_params);
      } else {
        window.open(url, '_blank');
      }
    }
  };
  onEvent('file_download_requested', onFileDownloadRequested);

  return telegramWebView;
};

// Initialize and export the WebView instance
const TelegramWebView = createTelegramWebView();
export default TelegramWebView;