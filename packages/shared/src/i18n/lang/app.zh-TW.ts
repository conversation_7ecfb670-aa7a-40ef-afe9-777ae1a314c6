import privacy from './privacy.zh-TW'
import terms from './terms.zh_tw'
import chat from './module/chat.zh-TW'
import share from './module/share.zh-TW'
import about from './module/about.zh-TW'
import faq from './module/faq.zh-TW'
import img from './module/img.zh-TW'

const translation = {
  img,
  approval_for_gift: '過審得🟡權益',
  gift: '獎勵',
  share,
  about,
  faq,
  chat,
  meta: {
    title: '夢幻AI伴侶',
    desc: '夢幻AI伴侶，AI聊天，酒館SillyTavern平替',
    copyright: '',
    privacy_policy: ''
  },
  common: {
    free_benefit_end: '免費權益不排隊有效期限: ',
    gen_img: '生圖',
    execute_success: '快照產生成功！ ',
    status_setting_title: '狀態列設定說明',
    status_setting_desc: `1.點選按鈕可對所有角色卡的狀態列進行隱藏或展示
    2.隱藏，代表之後AI回覆不再展示狀態欄
    3.展示，代表之後AI回覆文字下展示狀態欄

    說明：
    1.有的角色卡沒有狀態欄，不受狀態列展示、隱藏設定影響；
    2.也可在小程式-我的-設置，進行狀態列展示隱藏設定`,
    status_setting: '狀態列設定',
    save_success: '保存成功',
    update_rule: '更新規則',
    tpl: '模版',
    generate_status_bar: '一鍵產生狀態列',
    select_template: '選擇模板',
    status_bar: '狀態列',
    ai_generate_tips: '本功能為AI智能生成，AI將會結合您當前的聊天記錄情節發展，生成聊天整體的狀態欄，生成一次消耗600💎',
    ai_generate: 'AI生成中...',
    support_all_lang: '支援所有語言',
    upload: '上傳',
    more: '更多',
    less: '收起',
    go_setting: '去設定',
    public_test: '公測中',
    net_err: '網路異常',
    net_err_desc: `請嘗試以下方法後重試：
1. 切換vpn（翻牆）節點
2. vpn（翻牆）改為全域模式
若上述兩個方式未能解決，請[聯絡客服](https://t.me/ai_x01_bot)
    `,
    claim:'去領取',
    confirmBtn1: '去儲值',
    canbenefits: '權益記錄',
    benefits_give: '贈送權益',
    benefits_free: '免費權益',
    message: '訊息',
    title: '幻夢',
    back: '返回',
    model: '模式',
    comfirm: '確定',
    logout: '登出',
    regist: '註冊',
    login: '登錄',
    del: '刪除',
    cancel: '取消',
    update: '更新',
    submit: '提交',
    loading: '載入中...',
    load_err: '網路異常，請重試',
    retry: '重試',
    del_seccess: '删除成功',
    exec_err: '執行异常',
    tips: '提示',
    save: '儲存',
    back_home: '回到首頁',
    pay: '去儲值',
    save_back: '儲存返回',
    minutes_ago: '分鐘前',
    hours_ago: '小時前',
    days_ago: '天前',
    count_msg: '條對話',
    role_info: '角色訊息',
    role_introduction: '角色介紹',
    guide: '寫卡片教學(輕鬆學會)',
  },
  login: {
    tg_login: '透過Telegram登入',
    login_sucess: '登錄成功',
    login_failed: '登入失敗，請重試',
    login_title: '登錄你的賬號',
    email: '郵箱地址',
    password: '密碼',
    other_login: '或用以下賬戶登錄',
    no_account: '沒有賬號？',
    login_now: '立即註冊',
    regist_title: '註冊你的賬號',
    password_confirm: '密碼確認',
    verifyCode: '驗證碼',
    invite_code: '邀請碼 (選填)',
    regist: '註冊',
    password_len: '密碼至少要6位',
    password_not_match: '兩次密碼不一致',
    regist_success: "註冊成功，請登入～",
    regist_err: "抱歉，註冊出現問題，請稍後再試～",
    sended: "已發送",
    sended_desc: "驗證碼已發送到註冊的電子郵件地址，請登入你的電子郵箱查看",
    get_code: "獲取驗證碼"
  },
  setting: {
    theme: '主題',
    enable_nsfw: '開啓成人內容過濾',
    enable_nsfw_desc: '啟用成人內容即表示您確認您已年滿 18 歲。',
    setting: '設置',
    system: '跟隨系統',
    dark: '黑夜模式',
    light: '白天模式'
  },
  toast: {
    wait_data: '等待數據返回中，請稍後再試～',
    group_not_support: '直聊bot暫不支持群聊卡片',
  },
  nav: {
    img: '生圖',
    checkin_gift: '簽到領',
    index: '卡片',
    chat: '聊天',
    mine: '我的',
    pay: '儲值',
    create: '創建',
    claim_gift: '領',
    title: '夢幻AI伴侶',
    char: '角色列表',
    tag: '標籤管理',
    presets: 'presets',
    reg: '正則',
    operation: '運營',
    setting: '配置',
    createCard: '創建卡片'
  },
  footer: {
    about: '關於',
    contact: '聯繫我們',
    terms: '服務條款',
    privacy: '隱私政策',
  },
  index: {
    task: '福利任務',
    nsfw: '成人模式',
    more: '顯示更多',
    hide: '隱藏',
    loading: '加載中',
    no_card_load: '已加載到底部～',
    search_place_holder: '搜索角色名稱 / 關鍵詞 / 標籤',
    nsfw_alert_title: '需要登錄',
    nsfw_alert_desc: '需要登錄後，在設置裏面打開nsfw開關才能激活過濾功能',
    regist_title: '註冊你的賬號',
    author: '作者',
    filter: {
      tag: '標籤',
      chat_mode: '模式',
      independent_role: '獨立',
      speech_filter: '搶話',
      sort: '排序',
      default: '全部',
      all_model: '全部模式',
      select_tag: '選擇標籤',
      clear: '清除',
      mother_son: '母子',
      contrast: '反差',
      ntr: 'NTR',
      licentious: '淫亂',
      virgin: '處女',
      incest: '亂倫',
      fanfiction: '同人',
      gender: '男女'
    }
  },
  search: {
    search_res: '搜索結果',
    no_res: '搜索結果爲空，試試',
    create_role: '創建你的角色',
    noMsg: '不能爲空'
  },
  mine: {
    snapshoot1: '快照',
    card: '卡片記錄',
    snapshoot: '我的快照',
    extra_info_name_same: '已自動將補充簡介中的玩家名字替換為{{user}}',
    voice_title: '自動產生語音說明',
    voice_desc: `1. 開啟，會自動將「」()[]裡的文字內容產生語音（推薦，較快產生）
2. 關閉，會將所有文字產生語音（需花時間等待生成）`,
    voice: '語音設定',
    auto_generate_voice: '自動產生語音',
    switch_identity_tip: '切換身分提示',
    switch_identity_desc: `1. 切換身分後，上下文對話中的所有使用者名稱、頭像，都會修改
2. 切換身分後，使用者設定的補充內容生效`,
    switch_identity: '切換身分',
    cannot_delete_enabled: '身份選取狀態不支援刪除',
    switch_success: '切換成功',
    select_user: '選擇身分',
    vip_limit: '限時體驗',
    user_role: '玩家互動身份',
    select: '選擇',
    extra_info_desc: `1. 與AI聊天時，補充您扮演的玩家互動身分角色設定，會影響對話或劇情推進
2. 角色卡中對玩家互動身分的角色已經設定，可能存在衝突不完全生效的可能
3. 玩家互動身分補充設定的資訊生效內容token上限150`,
    extra_info_placeholder: `輸入玩家設定補充內容，token上限150
例如：{{user}}身高180cm，智商200
特殊說明：這裡的玩家名字禁止和暱稱相同，只能使用{{user}}`,
    extra_info_title_placeholder: `限制最多15個字`,
    extra_info_limit: '最多產生3條玩家補充訊息，同時生效的內容token上限150（支援多選）',
    extra_user_limit: '最多產生3個使用者身分',
    play_type: '玩法類型',
    next_receive_at: '下次领取時間',
    bg_transparent: '背景透明度',
    upload_limit_bg: '最多只能上傳2張聊天背景',
    chat_bg: '聊天背景',
    switch_language: '切換語言',
    use_common_bg: '使用個人通用聊天背景',
    benifit_consume: '權益消耗',
    benefits: '權益記錄',
    benefits_recharge: '儲值權益',
    benefits_give: '贈送權益',
    benefits_free: '免費權益',
    no_benefit: '暂无赠送權益',
    quota: '聊天额度',
    validity_period: '有效期',
    expire_amount: '過期鑽石數',
    expire_time: '過期時間',
    no_record: '沒有記錄',
    history_tips: '提示：只顯示25年1月9日及以後的最近3個月消費紀錄',
    expire_tips: '提示：只顯示25年1月9日及以後的最近3個月的過期記錄',
    date_format: 'YYYY年MM月DD日',
    no_more_list: '已加載到底部',
    type: '類型',
    card_name: '角色卡名',
    card_id: '角色卡ID',
    chat_mode: '聊天模式',
    consume_diamond: '消耗',
    diamond_consume_time: '消耗時間',
    diamond_expire: '過期紀錄',
    consume_record: '消費記錄',
    diamond_record: '鑽石記錄',
    show_chat_tips: '聊天小燈泡提醒開關',
    setting: '設定',
    nsfw_alert_desc: '需在首頁開啟nsfw開關，才能關閉圖片模糊功能',
    blur_img: '模糊圖片',
    upload_failed: '上傳失敗～',
    exceed_limit: '超出限額',
    nopay_card_limit: `非付費用戶最多只能創建6張角色卡，付費用戶創建卡片數量提升至15張`,
    nopay_card_limit1: `非付費用戶最多只能創建6張角色卡，付費用戶創建卡片數量提升至15張

【認證作者】可創建更多卡片哦❗️`,
    find_customer_service: '找客服認證',
    pay_card_limit: '付費用戶最多只能創建15張角色卡，VIP用戶創建卡片數量提升至30張。 VIP功能即將推出，敬請期待',
    only_three_group_card: '非VIP用戶最多只能創建3個群聊卡，VIP功能即將推出，敬請期待',
    vip_title: 'VIP功能',
    vip_desc: '開通VIP後可用，VIP功能即將推出，敬請期待',
    my_role: '我創建的角色卡',
    create: '建立',
    my_group: '我創建的群聊卡',
    upload_tip: '你還沒有創建的角色卡片，請點擊創建或者上傳',
    upload_group_tip: '还沒創建群聊，點選“創建群聊“創建',
    fav_role_tip: '沒有收藏的角色，在角色詳情頁可以收藏',
    no_content: '還沒有內容',
    recent_chat: '最近聊天',
    recent_chat_tip: '最近沒有聊天的角色',
    update_success: '更新成功～',
    public_success: '已提交，審核中～',
    create_failed: '創建失敗，請重試～',
    person_info: '個人信息',
    user_id: 'ID',
    nick_name: '昵稱',
    input_your_name: '請輸入你的角色名字',
    avatar: '頭像',
    update: '更新',
    beta: '內測',
    upload_card: '導入角色',
    upload_card_title: '導入角色說明',
    upload_card_desc: `本平台相容於其他平台的角色卡，支援[Character Card V1/V2 Specification](https://github.com/malfoyslastname/character-card-spec-v2/blob/main/spec_v2.md)標準卡片中最常見的PNG格式，Agnai、Tavern、SillyTavern、Risu、Chub、PygmalionAI、JanitorAI的角色請在這些平台匯出後在此上傳。`,
    my_share: '我的分享',
    user_share: 'Ta的分享',
    user_public: 'Ta的發布',
    fav: '我的收藏'
  },
  pay: {
    service: '聯絡客服',
    tg: 'Telegram群組',
    contact: '遇到問題？加入用戶交流群組',
    copy: '複製連結',
    copy_url: '複製網址',
    pay_tips: '⚠️ 付款提示',
    pay_tips_desc: '如遇付款異常，請將付款連結複製到其他瀏覽器開啟付款',
    wechat_btn: `「微信」備用通道`,
    alipay_btn: `「支付寶」備用通道`,
    recharge_explain1: '儲值完成後一般5分鐘內💎都會到帳。 <1>聯絡客服</1>',
    channel_bk: '備用通道',
    pay_wechat_channel_btn1: `微信支付`,
    pay_wechat_channel_btn2: `微信支付`,
    pay_alipay_channel_btn1: `支付寶支付`,
    pay_alipay_channel_btn2: `支付寶支付`,
    pay_channel_desc: '若無法完成儲值，可選擇【卡密儲值】100%成功哦',
    pay_methods: '支付方式',
    diamond_intro: '1鑽石=1金幣',
    exchanging: '兌換中',
    sorry_login_desc: '抱歉，登錄出現問題，請稍後再試～',
    card_active: '卡密兌換',
    input_code: '輸入迅雷發卡儲值後，頁面最下方的卡密',
    active: '兌換',
    charge_success: '儲值成功',
    charge_success_desc: `儲值成功，鑽石已發放到遊戲中~
        如未到賬，請聯繫客服`,
    charge_failed: '儲值失敗',
    charge_failed_desc: '抱歉，儲值出現問題，請重試～',
    charge_err: '儲值異常，原因：',
    remain_diamond: '剩餘鑽石',
    code_exchange: '卡密兌換',
    inventory: '儲值套餐',
    recharge_history: '儲值記錄',
    amount: '金額(USDT)',
    diamond: '鑽石',
    balance: '餘額',
    explain: '說明',
    create_time: '創建時間',
    expire_time: '過期時間',
    recharge_explain: '計費說明',
    diamond_intro_desc: `1. 1💎=1🟡
2. 原則上先消耗🟡再消耗💎，但若💎即將到期則先把這部分💎先消耗完，再消耗🟡

舉例：
（a）若💎🟡均為永久有效期，先消耗🟡，🟡消費為0後再消耗💎
（b）若💎🟡均有有效期，先消耗🟡；若💎即將過期（比🟡先過期），則先消耗即將過期的💎至歸0後，再消耗🟡
`,
    overtime: '支付超時',
    pay_failed: '支付失敗',
    copyed_success: '已複製到剪貼板',
    paying: '正在支付',
    usdt_pay_desc: '你可以從任意錢包或交易所轉賬至以上地址',
    time_remain: '剩餘支付時間',
    order_id: '訂單號',
    network: '網絡',
    charge_amount: '儲值鑽石數',
    tips: '溫馨提示：',
    tips1: '<0>請勿</0>向上述地址儲值USDT-ERC20及TRC20資產，否則資產<2>將不可找回</2>',
    order_created: '訂單已生成',
    order_created_desc: `支付連結已經推送到「幻夢AI伴侶」Bot中，請點擊下方支付按鈕，跳到Bot頁面，點擊支付連結支付
 （如果點擊按鈕無法跳轉，請關閉本小程序，在Bot找到推送連結繼續操作）`,
    pay: '立即跳轉',
    order_created_desc1: '請點擊下方支付按鈕，跳到支付頁面支付，如果已經支付，請關閉本窗口',
    pay1: '立即支付',
    charging: '正在儲值',
    charging_desc: '儲值成功請點擊"儲值完成"',
    charging_desc1: `1. 充值後最長需要10分鐘到賬，「我的」中「鑽石記錄」可以查看「充值記錄」。
2. 複製網址在外部「瀏覽器」（Safari、Chrome、Edge等）開啟可確保付款成功。
<1></1>
3. 仍然支付受阻，請點擊「備用通道」更換供應商後再試。
（提示：卡密購買中也有「{{payName}}」哦）`,
    finish_charge: '儲值完成',
    charge_err1: '儲值異常，請重試',
    btn1: '儲值遇到問題請點擊',
    method: '解決方案',
    method_desc: '支付不成功，可能是瀏覽器兼容導致。您可以複製下方鏈接，在其他瀏覽器（推薦chrome、QQ瀏覽器、UC瀏覽器、夸克瀏覽器等）中打開嘗試支付',
    charge_success_desc1: '鑽石已經到賬~',
    pay_success: '成功支付',
    no_money: '餘額不足',
    no_money_desc: '抱歉，你的USDT餘額不足，請儲值後重試',
    charge: '立即儲值',
    charge1: '支付寶儲值1',
    charge2: '微信儲值',
    usdt_charge: 'USDT儲值',
    credit_card: '信用卡儲值',
    start_pay: 'star儲值',
    ali2: '支付寶儲值2',
    bank: '銀聯支付',
    wechat_alipay: '卡密購買',
    wechat_alipay_subtitle: '(支持微信、支付寶)',
    wechat: '微信支付',
    wechat_subtitle: '(💎直接到帳)',
    alipay: '支付寶支付',
    alipay_subtitle: '(💎直接到帳)',
    wechat1: '微信掃碼支付1',
    Unionpay: '銀聯掃碼支付',
    copy_fail: '複製失敗！',
    usdt_warn: '我們到帳金額必須<1>與上述金額一致</1> 否則無法及時到帳<br/>交易所錢包請注意<5>扣除手續費後的金額</5> 與上述一致' ,
    min: '分',
    sec: '秒',
    copy_success: '已经复制到粘贴板，打开其他浏览器粘贴网址进行支付',
    copy_addr: '複製支付地址',
    purchase_tips: '（沒有？ <1>點此</1>購買）',
    exchange_guide_title: '激活碼使用方法',
    exchange_guide_desc: `
1. 點擊按鈕「微信/支付寶儲值」，在跳轉頁面購買後，輸入手機號或QQ號查詢卡密

2. 複製卡密

3. 在本頁面，點擊「卡密兌換」-- 輸入卡密

4. 【重點】購買後找不到卡密，可以聯絡客服 @xlfkwkf_bot (長按複製)`,
    view_btn: '查看圖例教程',
    exchange_btn: '卡密教學',
    recharge_btn: '儲值教學',
    recharge_guide_title: '儲值说明',
    recharge_guide_desc: `
微信/支付寶儲值：
卡密激活码儲值，儲值成功后在剩余钻石选择「卡密兑换」，输入激活码后钻石到账

微信扫码支付：
微信扫码直充，支付成功后钻石到账

支付宝扫码支付：
支付宝扫码直充，支付成功后钻石到账

USDT儲值：
交易所/钱包儲值（我们Polygon链，手续费很低，不支持其他链），支付成功后钻石到账`,
    guideClose: '關閉去“夢幻AI伴侶支付”',
    permanent_validity: '永久生效'
  },
  operation: {
    confirm: '確認',
    cancel: '取消',
    clear: '清空',
    save: '保存',
    edit: '編輯',
    refresh: '重新開始',
    send: '發送',
    copy: '複製',
    lineBreak: '換行',
  },
  dialog: {
    status_block_placeholder: '可選擇狀態列模版後，仍可做編輯狀態列（總token數上限500）',
    edit_status_block: '自訂狀態列說明',
    edit_status_block_desc: `**建立方式：**
    1.可以選擇狀態列模版，基於模版建立狀態列；同時，選擇狀態列模版後，可以使用AI一鍵產生狀態列
    2.可自訂狀態列，自行編輯狀態列及更新規則

    **收費說明：**
    1.選擇狀態列範本、自訂狀態列保存時都會扣除600💎
    2.選擇狀態列模版，AI一鍵生成狀態列時已扣除600💎（AI生成的內容未改動），此時保存不再扣除600💎；若AI一鍵生成狀態列後內容被改動，保存時會再扣除600💎。
    3.AI一鍵產生狀態列消耗600💎（每次點選AI一鍵產生狀態列都會消耗一次600💎）

    **自訂狀態列AI審核說明：**
    1.AI審核不通過，無法保存並使用
    2.AI審核通過，可保存並使用

    **狀態列隱藏、展示說明：**
    1.點擊按鈕可對所有角色卡的狀態列進行隱藏或展示（我的-設定）
    2.已隱藏，代表之後AI回覆不再展示狀態欄
    3.已展示，代表之後AI回復展示狀態列`,
    role_noconstant_content_placeholder: '輸入內容token上限1000',
    primy_key_placeholder: '多個關鍵字用逗號隔開，最多20個字',
    len_20: '內容長度不能超過20個字',
    cant_submit: '無法提交！ ',
    cant_submit_desc: `目前{{char}}、{{user}}存在格式錯誤，是否切換為正確的格式

點擊是，自動幫您更改為正確格式
點選否，自己手動更改`,
    yes: '是',
    no: '否',
    update_success: '更新成功',
    delComfirm: '是否要刪除？',
    play: '播放',
    right_title: '本卡片來源互聯網',
    right_title1: '本卡片來源作者創作',
    right_desc: '如有侵權請反饋官方刪除',
    right_desc1: '如有侵權請回饋官方，官方聯絡作者協助處理',
    avatar: '角色頭像',
    avatar_err: '未上傳圖片',
    cardName: '卡名字',
    require: '必填項',
    len_18: '不能超過18個字',
    name_place_holder: '用於卡片標題展示',
    chat_model: '輕聊天',
    role_model: '角色扮演',
    desc_placeholder: '包括AI角色、使用者角色（必須是名字，而非{user}）、關係、性格、外表、喜好、能力、年齡、性別、種族、國籍、民族等，此設定不對其他人展示',
    desc_title: '角色定義',
    example_title: '對話示例',
    desc_err_token: '超出最大tokens數',
    first_title: '第一條消息',
    first_desc: '開場白，角色初始打招呼的第一句話',
    name_len: '長度超出限制（15個字）',
    role_name: 'AI角色名',
    role_placholder: 'AI扮演的角色名字',
    intro_name: '卡片介紹',
    intro_placeholder: '詳細介紹角色特質、羈絆衝突、特殊玩法等，僅用於展示，不影響AI扮演發揮',
    personality_title: '性格',
    personality_placeholder: '對角色性格的設置',
    is_public: '是否公開？',
    scenario_title: '初始场景',
    simplt_intro: '營銷文案',
    limit_desc: '（{{n}}個字以內）',
    limit: '不能超過{{n}}個字',
    simplt_intro_placeholder: '在角色清單中顯示，簡介羈絆、衝突、角色魅力等引人之處。僅用於展示，不影響AI扮演發揮',
    voice: '聲音',
    status_block: '狀態欄',
    status_block_select: '請選擇狀態欄類型',
    normal: '標準',
    collapse: '摺疊',
    hide: '隱藏',
    status_sample: `範例：
心情：{心情}
性慾值：{性慾值}
地點：{地點}
小穴狀態：{小穴狀態}
和{{user}}的關係：和{{user}}的關係
外貌服裝：{外貌服裝}`,
 status_init_desc: `範例：
心情：興奮、焦慮
性慾值：33
地點：公共場合
小穴狀態：有些興奮濕潤
和{{user}}的關係：一起上學的同學,對他有些好感
外型服裝：乾淨整潔,髮梢有些微亂`,
 status_rule_desc: `範例：
性慾值取值範圍為0-100，初始值為33
性慾值更新規則：
當{{char}}的性慾被激發時上升、性慾被壓抑時下降，單次對話性慾值上下幅度不超過10
當{{char}}到達高潮時性慾值下降20
服裝內容應足夠詳細，包含內衣、襪子和配件，如果有裸露部位，要詳細描述`,
    status_tmp: '狀態欄模版',
    status_init: '初始狀態欄內容',
    status_rule: '狀態欄更新規則',
    label: '標籤',
    max_tags_err: '標籤數量不能超過12個',
    label_err: '非NSFW標籤需要勾選至少一個',
    cat: '分類',
    create_success: '創建成功～',
    creating: '正在創建..',
    create_fail: '創建失敗，請重試～',
    sum_token_exceed: '總tokens超出限制',
    next: '下一步',
    prev: '上一步',
    sum_cost: '總消耗',
    operate_success: '操作成功～',
    model_select: '模型選擇',
    review_reject: '拒絕原因',
    no_show_tip: '不再顯示此提示'
  },
  cardEdit: {
    public_reject_title: '提審違規',
    public_reject_desc: `您違反了公開發布角色卡的審核規範，已經被限制提交審核。

【申請恢復審核】請聯絡客服❗️`,
    public_reject_confirm: '聯絡客服',
    sys_tool: '系統與工具',
    play_type_title: '玩法類型說明',
    play_type_desc: `對手戲：AI不搶話，AI扮演一個或多個角色
推劇情：AI會寫所有劇情，玩家在裡面給予關鍵決策，AI進行補充
系統工具：各種AI助理、生成器、模擬器，包括小說生成、人物生成、故事生成、遊戲模擬、特定世界模擬等`,
    opponent_content: '對手戲',
    push_content: '推劇情',
    init_heat: '初始熱度值',
    final_heat: '当前熱度值',
    deduct_heat_title: '扣除熱度值說明',
    deduct_heat: '被扣除熱度值',
    deduct_heat_desc: `1. 被用戶點踩後會被扣除熱度值
2. 在AI判斷角色卡，使用所選模式不能推進劇情時會被扣除熱度值

改善方法：
1. 審核角色卡時，邏輯複雜的卡片不建議勾選極速模式及以下模式`,
    adapt_model_at_least_one: '至少要選擇1個適用模型',
    adapt_model_desc1: 'AI會根據極速模式推進劇情實際狀況影響角色卡熱度，若所選模式無法推進劇情，則會降低角色卡熱度。',
    adapt_model_title: '聊天模式說明',
    adapt_model_desc: `極速模式：速度超快，但無法處理複雜邏輯、推劇情會卡住
    魅惑模式：滿血版網紅模型DeepSeek外加深度調教，速度快，智商在線，可驅動部分複雜邏輯
    慾望模式：性格風騷，慾望加強，更容易推倒，可以處理複雜邏輯
    世界卡模式：高智商的模型和最佳破解調優，支援複雜卡片驅動，文筆炸裂，邏輯清晰
    超長記憶模式：群聊最佳模式，3倍超長記憶的高端模型，智商高、文筆好，適合驅動複雜卡片和推動超長劇情
    全能模式：集所有模式優點於一身，智商超高，記憶力超強，文筆超級炸裂，複雜角色扮演和宏大世界卡片模擬能力極強，經常出其不意讓你覺得她是真人`,
    md: 'Markdown格式支援查看',
    create1: '手動建立',
    tips_title: '說明',
    advanced_mode: '切換進階模式',
    role_noconstant_content_placeholder: '輸入內容token上限1000',
    primy_key_placeholder: '多個關鍵字用逗號隔開，最多20個字',
    len_20: '內容長度不能超過40個字',
    role_content_placeholder: '輸入內容無token上限，但算在總消耗的6000token中',
    constant_role_book: '持續生效世界書',
    key_role_book: '關鍵字世界書',
    comment_placeholder: '限制最多15個字',
    collaspe_err: '需要修復表單錯誤，才能折疊',
    form_error: '請檢查表單填寫是否正確',
    ai_name_repeat: '不能和AI角色名重複',
    no_user_name: '需使用{{user}}取代用戶角色名: {{userName}}',
    no_char_name: '需使用{{char}}取代AI角色名稱: {{charName}}',
    no_user_name1: '不能包含用戶角色名: {{userName}}',
    token_limit_exceeded: '不能超過{{limit}}tokens',
    adapModel: '適用模式',
    AIName_placeholder: 'AI扮演的角色名字',
    user_role_name: '用戶角色名',
    user_role_name_placeholder: '不會在聊天中顯示,但可幫AI更好產生對話內容',
    ai_formate: '回覆模式',
    example_placeholder: '對話示例對於AI回覆具有極強的指導性。AI會模仿這裡語言風格、內容結構，語言體現出的性格、喜好等個性\n需要符合卡模式，注意輕聊模式請使用直接文本和括號()',
    step1: '基礎設置',
    step2: '公開卡設置',
    public: '公開發布',
    example_dialog_user_placeholder: '這裡輸入用戶的對話',
    example_dialog_ai_placeholder: '這裡輸入AI扮演角色的對話',
    example_dialog_user_placeholder_chat: `張阿姨，你自己逛公園呀`,
    example_dialog_ai_placeholder_chat: `小明，要不咱們一起逛？ （範例，輕聊天「非對話」例如場景動作心理等放（）裡，「語言文字或對話」無修飾直接輸出`,
    replay_len: 'AI回覆長度',
    replay_len_title: '說明',
    replay_len_desc: `對話範例對於AI回應具有極強的指導性。 AI會模仿這裡語言風格、內容結構，語言體現出的性格、喜好等個性。

需要符合卡片模式：

1. 輕聊天，「非對話」例如場景動作心理等放（）裡，「語言文字或對話」無修飾直接輸出；

2. 角色扮演，「語言文字或對話」放「」裡；

這2種模式，都是強制給AI綁定一個固定角色，例如：黑寡婦、加勒比海盜NPC集合（集合代表一個角色）、一個系統、一個小說家。`,
    muilte_scenes_title: '對話場景',
    muilte_scenes_illustrate_title: '輕聊天說明',
    muilte_scenes_illustrate: '輕聊天，「非對話」例如場景動作心理等放（）裡，「語言文字或對話」無修飾直接輸出',
    muilte_scenes_init: '初始場景',
    muilte_scenes_init_desc: '碰巧在公園遇到小明（示例，小明是用戶名）',
    muilte_scenes_first_message: '第一条消息',
    muilte_scenes_first_message_desc: `（看到小明很開心）小明你在這裏做什麼？（示例，輕聊天「非對話」例如場景動作心理等放（）裏，「語言文字或對話」無修飾直接輸出`,
    muilte_scenes_first_message_role_desc: `看到小明很開心，“小明你在這裏做什麼？”（示例，角色扮演，「語言文字或對話」放“”裏）`,
    lang_title: '設置支持語言',
    auditing_desc: '審核中，審核完成才能再次發布哦',
    edit: '編輯',
    editing: '編輯中',
    auditing: '審核中',
    approve: '審核通过',
    reject: '審核拒絕',
    select_model: '請選擇建立方式',
    new_model: '新手模式',
    advanced_model: '高級模式',
    enable_beginer_tpl: '開啓新手模版',
    user: '用户',
    char: '角色',
    add_sample: '增加示例',
    add_dialog_context: '增加對話場景',
    normal_model: '全知視角',
    gender: '性別',
    gender_desc: '女（範例）',
    age: '年齡',
    age_desc: '18（範例，最小18歲）',
    personality: '性格',
    personality_desc: '好色，容易嫉妒（範例，可多個）',
    appearance: '外貌',
    appearance_desc: '身高165cm，長相甜美，高中校服JK款（範例）外貌（身形，長相，穿著）',
    sexual: '性癖',
    sexual_desc: '异味癖，對拒絕敏感（範例，可多個）',
    add: '增加',
    role_book: '世界書',
    order: '顺序：',
    enable: '開啓：',
    primy_key: '主要關鍵字：',
    sec_key: '可選過濾器：',
    trigger: '觸發機率(0-100)：',
    content: '内容：',
    status: '狀態：',
    constant: '常量',
    normal: '非常量',
    status_title: '狀態欄說明',
    status_desc: `1. 不支援標籤，請不要輸入任何標籤
        2. 系統預設增加\`\`\`的程式碼區塊支援，不需要單獨輸入
        
        範例：
        #{character name}}
👚服飾狀態: {character此刻詳細的服飾}
💭內心想法: {character此刻的內心想法}
        `,
    reply_title: '回覆模式說明',
    reply_desc: `1. 輕聊天：AI角色第一人稱視角，輸出{{char}}簡單對話，「非對話」例如場景動作心理等放（）裡，「語言文字或對話」無修飾直接輸出，對話中用「你」提及{{user}}
2. 角色扮演：AI 角色用第三人視角，輸出{{char}}的對話，“語言文字或對話”放“”，用“你”來提及{{user}}
3. 全知視角：無視角約定，無文字格式約定，無對話輸出要求，需自行約定。

1和2都是強制給AI綁定一個固定角色，例如：小張、黑寡婦、加勒比海盜（NPC集合，多人共同代表一個角色）、一個系統、一個小說家。

3適用於自行詳細定義的角色、系統、模擬器、小說家、敘述者、攝影機、監視器等。`,
    role_book_title: '世界書說明',
    role_book_desc: `！ ！高級功能，慎用！ ！
 關鍵字可觸發特殊場景玩法，同時可加強AI對關鍵字的記憶，世界書追平其他網路大平台AI角色卡功能

 建議：不懂世界書的用戶慎用`,
 role_book_constant_title: '持续生效世界书',
 role_book_constant_desc: `1. 不限制增加的条目个数
 2. 每一个持续生效世界书，条目内容输入不限制token长度，但算在总消耗的6000token中`,
 role_book_key_title: '关键词世界书',
 role_book_key_desc: `1. 每一个条目内容输入token上限1000
2. 不限制增加条目的个数，但是每次触发时，内容token上限1000
3. 设置多个关键词（逗号隔开）触发同一个条目，当聊天中最新的两轮对话（用户两条消息、AI两条消息）匹配其中一个关键词便触发该条目
4. 当最新的两轮对话（用户两条消息、AI两条消息）同一个关键词同时触发多个条目，或者多关键词触发多个条目，触发内容token上限都为1000
·顺序高的优先触发
·顺序相同时，若多个条目内容token上限不足1000，则全部触发；若多个条目内容token上限超过1000，超过的部分会被截断`,
  author: '作者名',
  showAuthor: '是否展示',
  author_desc: `不選擇展示，角色卡展示的作者名：匿名
 選擇展示，角色卡展示的作者名：您目前的使用者名稱`,
    data: '數據',
    cardName: '卡名字',
    uv: '用戶數',
    chatRound: '角色卡熱度',
    likeCount: '点赞數',
    auditng: '正在審核',
    audit_desc: '卡片正在審核中，審核結束才能操作',
    low_img: '上傳失敗',
    low_img_desc: '上傳圖片寬度不能低於400像素，高度不能低於600像素，請調整圖片品質或解析度後重試～',
    tips: `1. 審核通過時，有可能修改卡片的內容(圖片\狀態列\角色設定等)
2. 私有卡，僅自己可見
3. 連續累計被拒絕審核6次，會被限制公開發布`,
    del_failed1: '審核中的卡片不支援刪除',
    has_private_card_desc: `群聊中存在私有卡无法发布
建议：
1、私有卡先公开发布成功
2、公开发布成功的卡，可以通过“创建群聊-搜索”选择加入群聊，角色卡全部为公开卡可发布群聊。`,
  no_user_or_Char: '不能包含{{user}}，請刪除',
  max_9: '世界書條目最多不超過9個',
  max_constant_entries: '持續生效世界書最多不能超過3條',
  max_non_constant_entries: '非常量最多不能超過6條',
  quick_type: '快速輸入'
  },
  history: {
    no_archive: '沒有聊天記錄',
    group_card: '群聊卡',
    vip_feature: '發私照',
    load_archive: '讀取',
    archive: '讀取存檔',
    del_history: '刪除記錄',
    first_chat_at: '首次聊天',
    latest_chat_at: '最後聊天',
    tip: '請至少要選擇2個角色～',
    my_role: '建立私有卡',
    my_group: '建立私有群',
    group_title: '群聊創建&扣費規則',
    group_desc: `1. 可選2-4個AI角色卡，包含：幻夢平台上架的AI角色卡，自己創建的AI角色卡

2. 群組聊天體驗好的選卡建議：
 （1）相同故事背景，且彼此相互了解：眾所周知的故事（西遊記裡的人物，海賊王裡的角色），或者每張角色卡都設置相同的故事背景，或者在單一角色卡設定裡介紹其他角色卡的角色設定和關係
 （2）相同語言模式，相同回應格式，相似狀態列

3. 群組聊天時，使用者手動點擊AI角色頭像，被點擊的AI角色卡會發起回复，若點擊骰子，則隨機1個AI角色卡主動發起回复

4. 扣費說明：群聊時，用戶選擇某個聊天模式，每次AI角色卡發起回复，則扣除匹配的鑽石（舉例：群聊時，極速模式，用戶點擊某1個AI角色卡頭像，該AI角色發起回复，則扣除100鑽石）`,
    create_group: '創建群聊',
    group_scenario: '故事線開端',
    group_scenario_desc: '交代清楚角色間關係、所處場景、即將發生的事件等，便於後續開啟角色間的互動',
    rec_card: '推薦角色卡',
    my_card: '我的角色卡',
    select_role: '選擇群聊角色',
    select_role_desc: '只可選擇{{char}}為獨立角色的卡片',
    search_placeholder: '搜索角色卡',
    tip1: '群聊不能超過4個角色哦',
    no_create_card: '還沒創建卡片，請到"聊天"-"我的卡片"創建',
    search_empty: '搜索結果為空',
    name: '群聊名',
    name_placeholder: '在角色列表中顯示，名字起得好會有更多人玩',
    story_bg: '群聊介紹',
    story_bg_desc: '詳細介紹角色特徵、羈絆衝突、特殊玩法等，僅用於展示，不影響AI扮演發揮',
    scenario: '初始場景',
    scenario_desc: '輸入初始場景描述，故事開始的場景，展示在群聊詳情頁'
  },
  errorMessage: {
  },
  lang: {
    zh: '简体中文',
    en: 'English',
    'zh-TW': '繁體中文'
  },
  lan_short: {
    zh: '简体',
    en: 'En',
    'zh-TW': '繁体'
  },
  privacy,
  terms,
  recentChat: {
    del_chat: '删除记录',
    export_chat: '導出',
    export_card: '導出卡片'
  },
  sort: {
    approved: '審核通過',
    reject: '審核拒絕',
    no_publish: '未發布',
    default: '預設'
  }
}

export default translation
