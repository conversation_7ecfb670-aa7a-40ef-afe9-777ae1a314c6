// app/components/ui/Card.tsx
"use client";
import Image from 'next/image'
import React, { <PERSON><PERSON><PERSON><PERSON>, useContext, useEffect, useState } from 'react';
import { PencilSquareIcon, TrashIcon, ShieldExclamationIcon, ArrowRightStartOnRectangleIcon } from '@heroicons/react/24/solid'
import { useParams, usePathname, useRouter } from 'next/navigation';
import s from './style.module.css'
import cn from 'classnames';
import useComfirm from '../../hook/useComfirm';
import useRequest from '../../hook/useRequest';
import Toast from '../../ui/toast';
import useCreateCard from '../../hook/useCreateCard'
import { AuthContext } from '../../authContext'
import useDialogAlert from '../../hook/useDialogAlert';
import { useTranslation } from 'react-i18next'
import useLightBox from '../../hook/useLightBox'
import AnalysisBtn from './analysisBtn'
import useCreateGroup from '@share/src/hook/useCreateGroup'
import { imgBlur } from '@share/src/module/urlTool';
import { preload } from 'swr'
import Tags from './tags';
import Link from 'next/link';
import useLoadArchive from '@share/src/hook/useLoadArchive';
import useLinkToChat from '@/app/hook/useLinkToChat'
import {LoginStatus} from '@share/src/authContext'
import { tgWeb } from '@/app/module/global';
import { screenW } from '@share/src/module/global';


// 获取from_bot和from_channel,对从bot搜索入口唤起的来源进行处理
let fromBot = ''
let fromChannel = ''
if(typeof window !== 'undefined') {
    const searchParams = new URLSearchParams(location.search);
    fromBot = searchParams.get('bot_username') || ''
    fromChannel = searchParams.get('from_channel') || ''
}

export type RoleType = {
    name: string;
    role_name: string;
    card_name: string;
    role_avatar: string;
    introduction: string;
    id: number;
    isEdit?: boolean,
    speakList?: string[]
    sub_tags: string[]
    simple_intro?: string
    nsfw: boolean
    admin?: boolean
    audit_status?: string
    audit_reason?: string
    author?: string
    author_name?: string
    scenario?: string
    roles: any[]
    public_role_id?: number
    isShowExport?: boolean
    isShowExportCard?: boolean
    isShowPublic?: boolean
    isShowEdit?: boolean
    isShowDel?: boolean
    isShowDelChatHistory?: boolean
    author_id?: number
    support_photo: boolean
    private_card: boolean
    // 是否需要模糊图片
    image_nsfw: boolean
    isShowTags: boolean
    isShowArchive: boolean
    // 角色卡奖励提示
    reward_desc?: string
    // 是否显示版权警告
    showCopyRight?: boolean
    // 是否显示快照标签
    showSnapshoot?: boolean
    // 是否跳过详情页直接进入聊天页面
    direct?: boolean,
    user_snapshot_id?: number
};

type CardProps = {
    role: RoleType
    reflesh?: Function
    subTagList?: string[],
    isRouterReplay?: boolean
    modeType?: string,
    isBlurImg?: boolean,
    switchSubtags?: any
    config?: any
};

const editBtn = 'flex rounded hover:bg-gray-200 dark:hover:bg-gray-700 items-center p-1 text-blue-500'
// 生命周期内只预取一次
let ifPrefetchOne = false;
const Card = ({role, reflesh, subTagList, modeType = 'single', isBlurImg = false, switchSubtags, config}: CardProps) => {
    const { t } = useTranslation()
    const router = useRouter();
    const params = useParams()
    const lang = params.lang as string
    const comfirm = useComfirm();
    const request = useRequest();
    const [cardLabels, setCardLabels] = useState([])
    // const createGroup = useCreateGroup();
    const [display, setDisplay] = useState(true)
    // const [cardLabels, setCardLabels] = useState(['BSDM', 'NTR', '同人', '角色扮演', '角色扮演', '人妻', '校园'])
    // const groupAvatar = role.roles?.map(role => role.role_avatar)
    const createCard = useCreateCard();
    const dialogAlert = useDialogAlert();
    const lightBox = useLightBox();
    const isGroupCard = modeType === 'group'
    const pathname = usePathname();
    const auth = useContext(AuthContext);
    const user = auth?.user
    const loadArchive = useLoadArchive()
    const linkToChat = useLinkToChat()
    const [showPublicTask, setShowPublicTask] = useState(user?.publish_task_switch)
    const imgSize = screenW > 1024? {width: 200, height: 300} : {width: 100, height: 150}
    const groupImgSize = screenW > 1024? {width: 100, height: 150} : {width: 50, height: 75}

    useEffect(() => {
        if(ifPrefetchOne) return;
        router.prefetch(geenerateUrl(role.id))
        ifPrefetchOne = true;
    }, [])
    const geenerateUrl = (id: number) => {
        // 由于chat页面有切换语言选项，切换后，需要更新来源url，所以增加from
        const searchStr = window.location.search
        const fromUrl = `${pathname}${searchStr}`
        return `/${lang}/role?from=${encodeURIComponent(fromUrl)}&type=${modeType}&id=${id}&blur=${isBlurImg? 1 : 0}`
    }
    const onSlectRole = (id: number) => {
        console.log('onSlectRole', id);
        // 管理后台，只支持开发环境
        if(role.admin) {
            const prefix = isGroupCard? 'g_' : 'r_'
            const url = process.env.NEXT_PUBLIC_ENV === 'dev'? `https://t.me/role_youzi_bot/tavern?startapp=u_1-${prefix}${id}` : `https://t.me/FancyTavernBot/tavern?startapp=u_1-${prefix}${id}`
            window.open(url)
        } else if(role.direct) {
            // 如果是从快照进入，newStart=1，新开存档
            role.user_snapshot_id && linkToChat.push({isGroupCard, id, sid: role.user_snapshot_id, newStart: 1})
        } else {
            // 从bot搜索入口打开，直接跳回bot
            if(fromBot) {
                if(isGroupCard) {
                    Toast.notify({
                        type: 'info',
                        message: t('app.toast.group_not_support'),
                    })
                } else {
                    window.open(`https://t.me/${fromBot}?start=u_0-r_${role.id}`)
                }
                return;
            }
            // 预加载角色数据
            const url = modeType === 'group'? `/roles/group/detail?group_id=${role.id}` : `/roles/detail?role_id=${role.id}`
            preload(url, request)
            router.push(geenerateUrl(id))
        }
    }
    const delHandle = async (id: number) => {
        const isDel = await comfirm.show({});
        console.log('isDel', isDel);
        
        if(isDel?.confirm) {
            const fd = new FormData();
            if(isGroupCard) {
                fd.append('group_id', String(id));
            } else {
                fd.append('role_id', String(id));
            }
            
            const res = await request(isGroupCard?'/roles/group/disable' : '/roles/disable', {
                method: 'POST',
                body: fd
            })
            if(res.error_code && res.error_code === 1002) {
                Toast.notify({
                    type: 'error',
                    message: t('app.cardEdit.del_failed1'),
                })
            } else if(!res.error_code)  {
                Toast.notify({
                    type: 'success',
                    message: t('app.common.del_seccess'),
                })
                reflesh && reflesh(id);
                console.log('res', res);
            } else {
                Toast.notify({
                    type: 'error',
                    message: t('app.chat.del_failed')
                })
            }
        }
    }
    const delHistory = async (id: number) => {
        const isDel = await comfirm.show({});
        console.log('isDel', isDel);
        if(isDel?.confirm) {
            const res = await request(`/chat/history/delete?mode_target_id=${id}&mode_type=${modeType}`, {
                method: 'POST'
            })
            if(res.error_code == 0) {
                Toast.notify({
                    type: 'success',
                    message: t('app.common.del_seccess'),
                })
                reflesh && reflesh(id);
            } else {
                Toast.notify({
                    type: 'error',
                    message: t('app.common.exec_err'),
                })
            }
        }
    }

    const exportCard = async (id: number) => {
        window.open(process.env.NEXT_PUBLIC_API_HOST + '/role_card/export/img?role_id=' + id);
        // const res = await request()
        // Toast.notify({
        //     type: 'success',
        //     message: '已经导出卡片～',
        // })
        // console.log('res', res);
    }

    const checkIsAudit = () => {
        if(role.audit_status == 'auditing') {
            dialogAlert.show({
                alertStatus: 0,
                title: t('app.cardEdit.auditng'),
                desc: t('app.cardEdit.audit_desc')
            })
            return true
        }
        return false
    }
    const alertQuota = (desc: string) => {
        dialogAlert.show({
          title: t('app.mine.vip_title'),
          desc: desc,
          alertStatus: '-1'
        });
    }
    const showAnalystic = role?.public_role_id !== undefined && role?.public_role_id > 0;
    // 如果登录失败或者登录成功，都展示图像，登录中不展示
    const isShowBlurImg = auth?.loginStatus === LoginStatus.failed || auth?.loginStatus === LoginStatus.success
    // 登录失败展示模糊图片
    const canBlurImg = auth?.loginStatus === LoginStatus.failed? true : !user?.show_nsfw_image;
    return <>
    {display && <div className={cn('relative overflow-hidden cursor-pointer w-full max-w-full rounded-md dark:border dark:border-gray-500 dark:bg-gray-900 bg-white', !role.admin && 'lg:flex lg:flex-col lg:items-center lg:justify-center lg:pt-0 lg:!border-0 group')} onClick={() => onSlectRole(role.id)}>
            {/* {role.nsfw && <div className={cn('absolute right-0 text-sm top-0 opacity-75 border rounded inline-block w-10 flex items-center px-1 border-gray-800 bg-gray-900 text-orange-500')}>nsfw</div>} */}
            {!role.admin && !role.isEdit && isGroupCard && <span className='absolute right-0 top-0 bg-red-500 text-white text-xs px-0.5'>{t('app.history.group_card')}</span>}
            {(role?.support_photo) && <span className='absolute right-0 top-0 bg-red-500 text-white text-xs px-0.5'>{t('app.history.vip_feature')}</span>}
            <div className={cn("w-[120px] h-[180px] overflow-hidden dark:bg-gray-800 bg-gray-300 mr-3 float-left relative", !role.admin && 'lg:w-full lg:aspect-square lg:h-auto lg:mx-0')}>
                {role.role_avatar && <Image key={role.role_avatar} className={cn('w-[120px] h-[180px] max-w-none object-top', !role.admin && 'lg:w-full lg:aspect-square lg:h-auto lg:transition-transform lg:duration-300 lg:group-hover:scale-105')} style={{objectFit: "cover"}} src={(isShowBlurImg || role.admin)? imgBlur({url: role.role_avatar, isBlurImg: role.image_nsfw && isBlurImg, canBlur: canBlurImg}) : '/dot.png'} width={imgSize.width} height={imgSize.height} quality={90} alt={role.role_name} onClick={(e) => {
                    // 只支持管理后台
                    if(!role.admin)return;
                    e.stopPropagation()
                    lightBox.show({src: role.role_avatar || '/dot.png'});
                }} />}
                {
                    isGroupCard && <div className={cn(!role.admin && 'lg:grid lg:grid-cols-2 lg:gap-2 lg:p-2')}>{role?.roles?.map((_role: RoleType, index: number) => {
                        return <Image key={index} className={cn('m-0.5 inline-block rounded max-w-none w-[56px] h-[86px] object-top', !role.admin && 'lg:w-full lg:m-0 lg:aspect-square lg:h-auto lg:transition-transform lg:duration-300 lg:group-hover:scale-105')} style={{ objectFit: "cover" }} src={(isShowBlurImg || role.admin)? imgBlur({url: _role.role_avatar || '/dot.png', isBlurImg: _role.image_nsfw && isBlurImg, canBlur: canBlurImg}) : '/dot.png'} width={groupImgSize.width} height={groupImgSize.height} alt={_role.role_name} />
                    })}</div>
                }
                {role.private_card && <p className='absolute right-0 bottom-0 rounded-b text-[10px] text-center leading-4  text-white bg-purple-600/50 rounded px-0.5'>{t('app.chat.private_card')}</p>}
                {role.showSnapshoot && <div className='absolute right-0 bottom-0 text-[10px] text-center leading-4  text-white bg-purple-600/75 rounded-tl px-0.5'>{t('app.mine.snapshoot1')}</div>}
            </div>
            
            <div className={cn("relative mr-2 overflow-hidden h-[180px] py-1", !role.admin && 'lg:mx-0 lg:mt-2 lg:h-[140px] lg:w-full lg:px-2')}>
                <div className="name text-lg dark:text-gray-100 truncate flex items-center">
                    <span className='hover:underline inline-block flex-1 text-ellipsis overflow-hidden'>{role.card_name || role.role_name || role.name}</span>
                </div>
                {role?.author_name && !tgWeb && <div className='text-xs text-gray-400 mb-1'>{t('app.index.author')}: {role.author_id === 0? role.author_name : <Link onClick={(e) => {e.stopPropagation()}} href={`/user/${role.author_id}/card`} prefetch={false} className='hover:underline'>{role.author_name}</Link>} {role.admin && <span className='ml-2'> 用户id: {role.author_id}</span>}</div>}
                {isGroupCard ? <div className={cn("line-clamp-3 lg:line-clamp-2 hover:underline desc text-sm dark:text-gray-200 text-gray-600 h-22")}>{role.scenario}</div> : <div className={cn("line-clamp-3 lg:line-clamp-2 hover:underline desc text-sm dark:text-gray-200 text-gray-600 h-22")}>{role.simple_intro || role.introduction}</div>}
                
                {role.isShowTags && <Tags className={cn(role.isEdit && 'w-48', 'max-h-10 overflow-hidden absolute bottom-0 mr-5 mb-1')} tags={role.nsfw? ['NSFW', ...role.sub_tags] : role.sub_tags} switchSubtags={switchSubtags} />}

                {role.isEdit && <div onClick={(e) => {e.stopPropagation()}} className='absolute -bottom-3 -right-2.5 p-3 text-white rounded text-xs flex'>
                {showAnalystic && <AnalysisBtn role={role} /> }
                {role.isShowPublic && <button className={cn(editBtn, 'relative')} onClick={async (e) => {
                    e.stopPropagation();
                    if(checkIsAudit()) return;
                    let isSuccess;
                    if(isGroupCard) {
                        isSuccess = await createCard.showPublicGroup({msg: {...role, subTagList: subTagList, isPublic: true}})
                    } else {
                        isSuccess = await createCard.show({msg: {...role, speakList: role.speakList, subTagList: subTagList, isPublic: true, config, public_role_id: role?.public_role_id}})
                    }
                    isSuccess && reflesh && reflesh();
                    setShowPublicTask(false)
                }}>
                    {t('app.cardEdit.public')}
                    {role?.public_role_id === 0 && showPublicTask && <span className='text-xs absolute -top-3 -left-4 text-gray-600 dark:text-white w-24'>{t('app.approval_for_gift')}</span>}
                </button>}
                
                {role.isShowEdit && <button className={cn(editBtn)} onClick={async (e) => {
                    e.stopPropagation();
                    if(checkIsAudit()) return;
                    let isSuccess;
                    if(isGroupCard && !role?.admin ) {
                        alertQuota(t('app.mine.vip_desc'))
                        // isSuccess = await createGroup.show({msg: {create_roles: [], id: role.id, isEdit: true}})
                    } else {
                        isSuccess = await createCard.show({msg: {...role, speakList: role.speakList, subTagList: subTagList}})
                    }
                    isSuccess && reflesh && reflesh();
                }}>{t('app.cardEdit.edit')}</button>}
                
                {role?.isShowArchive && <button className={cn(editBtn)} onClick={async (e) => {
                    e.stopPropagation();
                    const res = await loadArchive.show({roleId: role.id, cardName: role.card_name || role.name, modeType})
                    if(res) {
                        linkToChat.push(res)
                    }
                }}>{t('app.history.archive')}
                </button>}
                {role?.isShowDelChatHistory && role?.audit_status !== 'approved' && <button className={cn(editBtn)} onClick={(e) => { e.stopPropagation(); delHistory(role.id)}}>{t('app.history.del_history')}</button>}
                {role?.isShowDel && <button className={cn(editBtn)} onClick={(e) => { e.stopPropagation(); delHandle(role.id)}}>{t('app.common.del')}</button>}

                {/* {role.isShowExport && <button className={cn(editBtn)} onClick={() => {exportChat(role.id)}}>{t('app.recentChat.export_chat')}</button>} */}
                {role.isShowExportCard && <button className={cn(editBtn)} onClick={() => {exportCard(role.id)}}>{t('app.recentChat.export_card')}</button>}
                </div>}
                
                {role.showCopyRight && <button onClick={(e) => {
                    e.stopPropagation();
                    const title = role.author_id === 0? t('app.dialog.right_title') : t('app.dialog.right_title1')
                    const desc = role.author_id === 0? t('app.dialog.right_desc') : t('app.dialog.right_desc1')
                    dialogAlert.show({
                        title: title, desc: desc, alertStatus: '-1'
                    });
                }} type='button' className='absolute right-0 bottom-0 lg:right-1.5 lg:bottom-1.5'><ShieldExclamationIcon className='text-green-900/20 hover:text-lime-500 dark:text-green-900/90 dark:hover:text-lime-500 w-4 h-4' /></button>}
            </div>
            {/* 卡片状态 */}
            {!role.admin && role.isEdit && <div className='absolute text-white right-0 top-0 flex items-center text-xs'>
                {role.audit_status == 'editing' && <span className='bg-yellow-500 p-1 inline-block'>{t('app.cardEdit.editing')}</span>}
                {role.audit_status == 'auditing' && <span className='bg-blue-500 p-1 inline-block'>{t('app.cardEdit.auditing')}</span>}
                {role.audit_status == 'approved' && <span className='bg-green-500 p-1 inline-block'>{t('app.cardEdit.approve')}</span>}
                {role.audit_status == 'rejected' && <div className='flex items-center bg-red-500 p-1 inline-block' onClick={(e: any) => {
                    e.stopPropagation();
                    dialogAlert.show({
                        title: t('app.dialog.review_reject'), desc: role.audit_reason, alertStatus: '-1'
                    });
                }}>
                    {t('app.cardEdit.reject')}
                    <span className='inline-block ml-1 text-center text-gray-500 hover:text-white border-gray-500 hover:border-white rounded-full border w-4 h-4 text-xs text-white border-white'>?</span>
                </div>}
                {role?.reward_desc && <Link onClick={(e: any) => { e.stopPropagation(); }} href={`/${lang}/mine/chat`} className='absolute bg-red-500 w-28 text-center rounded-l-lg top-6 right-0 text-white p-1 text-xs underline'>{role?.reward_desc}</Link> }
            </div>}
        </div>}
    </>
};

export default Card;
