'use client'
import classNames from 'classnames'
import type { ReactEventHandler, ReactNode } from 'react'
import React, { useContext, useEffect, useState } from 'react'
import { createRoot } from 'react-dom/client'
import Confirm from '@share/src/ui/dialog/Confirm'
import { useTranslation, Trans } from 'react-i18next'
import Toast from '@share/src/ui/toast'

type Iresult = {
  confirm: boolean,
  cancel: boolean,
  ignore: boolean
}
let promise: Promise<Iresult> | null;
type IParam = {
  title?: string
  url?: string
  payName?: string
  comfirmBtn?: string
  showCancelBtn?: boolean
  icon?: ReactNode
  cancelBtn?: string,
  isShowIgnore?: boolean,
  showIcon?: boolean,
  isCloseIconShow?: boolean,
  // 取消按钮样式
  cancelBtnStyle?: string
}
const useComfirm = (type: 'normal' | 'gift' = 'normal') => {
  const { t } = useTranslation()
  return {
    show: ({
      title,
      url = '',
      payName,
      comfirmBtn,
      showCancelBtn,
      icon,
      cancelBtn,
      isShowIgnore = false,
      showIcon = true,
      isCloseIconShow = true,
      cancelBtnStyle = ''
    }: IParam) => {
      console.log('payName', payName);
      const copyAddress = (url: string) => {
          navigator.clipboard.writeText(url).then(() => {
            Toast.notify({type: 'success', message: t('app.pay.copyed_success')})
          }).catch(err => {
              console.error('复制失败！', err);
          });
        }
      if(promise) {
        return promise;
      } else {
        let holder = document.createElement('div')
        holder.className = 'absolute z-50 w-full h-full left-0 top-0'
        document.body.appendChild(holder)
        const root = createRoot(holder)
        let ignore = false;
        promise = new Promise<Iresult>((resolve, reject) => {
          const onComfirm = (file: any) => {
            resolve({
              confirm: true,
              cancel: false,
              ignore: ignore
            })
            root.unmount();
            document.body.removeChild(holder);
            promise = null;
          }
          const onClose = () => {
            root.unmount();
            document.body.removeChild(holder);
            resolve({
              confirm: false,
              cancel: false,
              ignore: ignore
            });
            promise = null;
          }
          const onCancel = () => {
            root.unmount();
            document.body.removeChild(holder);
            resolve({
              confirm: false,
              cancel: true,
              ignore: ignore
            });
            promise = null;
          }
          const ignoreCb = (_ignore: boolean) => {
            ignore = _ignore;
          }
          let component = <Confirm onCancel={onCancel} cancelBtnStyle={cancelBtnStyle} isOpen={true} onConfirm={onComfirm} onClose={onClose} comfirmBtn={comfirmBtn} showCancelBtn={showCancelBtn} icon={icon} cancelBtn={cancelBtn} isShowIgnore={isShowIgnore} ignoreCb={ignoreCb} showIcon={showIcon} isCloseIconShow={isCloseIconShow}>
          <h1 className='text-base font-semibold leading-6'>{title || t('app.common.del')}</h1>
          <div className='text-sm mt-2 whitespace-pre-wrap pr-4'>
            <Trans i18nKey="app.pay.charging_desc1" t={t}>
1. 充值后最长需要10分钟到账，“我的”中“钻石记录”可以查看“充值记录”。
2. 复制网址在外部「浏览器」（Safari、Chrome、Edge等）打开可确保支付成功。
<div className="mt-2 flex items-center gap-1">
  <input type="text" value={url} className='rounded-full border border-gray-300 dark:border-gray-600 dark:bg-gray-800 bg-gray-100 dark:text-gray-300 text-gray-700 px-3 py-1.5 text-xs w-60 focus:outline-none focus:ring-1 focus:ring-purple-500 focus:border-purple-500 hover:border-gray-400 dark:hover:border-gray-500 appearance-auto'/>
  <button onClick={() => copyAddress(url)} className='px-2 py-1 bg-purple-500 text-white rounded ml-1 text-xs'>{t('app.pay.copy_url')}</button>
</div>
3. 仍然支付受阻，请点击“备用通道”更换供应商后再试。
（提示：卡密购买中也有「{{payName}}」哦）
            </Trans>
          </div>
          </Confirm>
          root.render(component);
        })
        return promise;
      }
    },
    hide: () => {
      // if (holder) root.unmount();
    }
  }
}

export default useComfirm
