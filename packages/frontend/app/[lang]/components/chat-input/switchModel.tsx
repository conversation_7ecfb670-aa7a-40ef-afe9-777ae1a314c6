'use client'
import cn from 'classnames'
import React, { useCallback, useContext, useEffect, useState } from 'react'
import useRequest from '@little-tavern/shared/src/hook/useRequest'
import Toast from '@little-tavern/shared/src/ui/toast'
import { AuthContext } from '@little-tavern/shared/src/authContext'
import Image from 'next/image'
import { useTranslation } from 'react-i18next'
import { createPortal } from 'react-dom';
import Modal from '@share/src/ui/dialog/Modal'
import useDialogAlert from '@share/src/hook/useDialogAlert'
import { useParams, useRouter } from 'next/navigation';
import { CheckCircleIcon } from '@heroicons/react/24/solid'
import { LockOpenIcon, ArrowsUpDownIcon } from '@heroicons/react/24/solid'
import useComfirm from '@share/src/hook/useComfirm';
import { runsOnServerSide } from '@little-tavern/shared/src/module/global'
import rocketSvg from './icons/rocket.svg'
import turtleSvg from './icons/turtle.svg'
import useLocalStorage from '@share/src/hook/useLocalStorage'

export enum EventType {
    // chat_entry:  表示用户进入聊天页面时的模型 （这时from_model和to_model的值是一样，你任选一个读就可以）
    chat_entry = 'chat_entry',
    // chat_man : 表示用户手动切换了模型
    chat_man = 'chat_man',
    // chat_auto： 表示自动切换了模型
    chat_auto = 'chat_auto',
    // chat_auto_： 表示免费额度不足自动切换了模型
    chat_channel_auto = 'chat_channel_auto',
    // 聊天界面领取了免费模型，自动切换到含免费额度最好的模型
    chat_auto_free = 'chat_auto_free'
}

const SwitchModel = ({ supportMids, setModelEventMap, chatList, modeType, conversationId, roleId, groupId, isResponsing, chatTimes, setChatTimes }: any) => {
    const [isEdit, setIsEdit] = useState(false)
    const { t } = useTranslation()
    const params = useParams()
    const lang = params.lang as string
    const auth = useContext(AuthContext);
    const user = auth?.user;
    const payedUser = user?.payed_user;
    const mid = user?.user_model
    // 如果能有免费聊天权益，说明是新用户
    const [isNewUser, setIsNewUser] = useLocalStorage('new_channel_user', false)
    // 免费聊天次数列表
    const [chatBalance, setChatBalance] = useState<any>({})
    const allModels = user?.chat_products?.map((item: any) => {
        return {
            ...item,
            sum_remain_times: chatBalance[item.mid]?.sum_remain_times,
            sum_total_times: chatBalance[item.mid]?.sum_reward_times,
        }
    });
    // 免费额度模型
    const freeModels = allModels?.filter((model) => {
        return model.sum_total_times > 0
    }) || []
   
    const router = useRouter();
    const request = useRequest();
    const dialogAlert = useDialogAlert();
    const confirm = useComfirm();
    const [isShowGuideSwitchModel, setIsShowGuideSwitchModel] = useState(!runsOnServerSide && localStorage.getItem('showGuideSwitchModel') === null)
    // 支持的模型
    const models = allModels?.filter((item: any) => supportMids ? supportMids?.includes(item.mid) : true)
    // 用户的模型是否在支持的模型中
    const isCurModelInSupport = models?.some((item: any) => item.mid == mid)
    // 不支持的模型
    const notPassModels = allModels?.filter((item: any) => supportMids?.indexOf(item.mid) === -1)
    const notPassModelNames = notPassModels?.map((item: any) => item.model_name)
    // 默认推荐模型
    let suggestModel = null;
    let exitFreeRecModel = false;
    if (payedUser) {
        // 获取支持的models里面price最小的模型
        suggestModel = models?.sort((a: any, b: any) => a.price - b.price)[0]
    } else {
        // 获取支持的models里面permission==="ALL_USER"模型列表
        const allUserModels = models?.filter((item: any) => item.permission === 'ALL_USER')
        // 如果存在免费模型列表，获取price最小的模型
        if (allUserModels && allUserModels?.length > 0) {
            suggestModel = allUserModels?.sort((a: any, b: any) => a.price - b.price)[0]
            exitFreeRecModel = true
        } else {
            // 获取所有models里面permission==="ALL_USER"模型列表
            const allModelsAllUserModels = allModels?.filter((item: any) => item.permission === 'ALL_USER')
            // 如果不存在支持的免费模型，获取价格最高的可用模型
            suggestModel = allModelsAllUserModels?.sort((a: any, b: any) => b.price - a.price)[0]
        }
    }

    // 如果用户选择的模型下架，切换到推荐模型
    const curModel: any = allModels?.find((item: any) => {
        return item.mid == mid
    });
    useEffect(() => {
        if (!curModel) {
            dialogAlert.show({
                title: t('app.common.tips'),
                desc: t('app.chat.model_not_exist', { name: suggestModel?.model_name }),
                alertStatus: '2'
            })
            changeModel({ mid: suggestModel?.mid, event_type: EventType.chat_auto })
        }
    }, [curModel])

    //  为null时表示用户当前选的模型是角色卡支持的，不用提示用户切换模型
    // "switch_model": {
    //   target_model: "m17",  # 需要帮用户切换到的目标模型
    //   need_recharge_first: false     # false 表示不需要提示用户需要先充值。  true表示需要提示用户先进行充值，才能切换 （说明  target_model是个付费模型并且当前用户还不是充值用户）
    //  }  

    const updateModelEventMap = (event_type: string, mid?: string, to_chat_channel?: string, from_chat_channel?: string) => {
        setModelEventMap((prev: any) => {
            if (chatList.length === 0) return {}
            const lastMsgId = chatList[chatList.length - 1].message_id;
            const last = prev[lastMsgId];
            const modeEvent = {
                event_type: event_type,
                from_model: user?.user_model,
                from_model_name: allModels?.find((item: any) => item.mid == user?.user_model)?.model_name,
                to_model: mid,
                to_model_name: allModels?.find((item: any) => item.mid == mid)?.model_name,
                chat_channel: user?.chat_channel,
                from_chat_channel: from_chat_channel,
                to_chat_channel: to_chat_channel
            };
            if (last && last.length > 0) {
                last.push(modeEvent);
            } else {
                prev[lastMsgId] = [modeEvent]
            }
            return prev
        })
    }
    const changeModel = async ({ mid, event_type, chat_channel = user?.chat_channel, autoClose = true }: any) => {
        if (mid === user?.user_model && chat_channel === user?.chat_channel) return
        // console.log('isResponsing', isResponsing)
        if (isResponsing) {
            Toast.notify({
                type: 'info',
                message: t('app.chat.switch_failed1'),
                duration: 3000
            })
            return
        }
        Toast.showLoading('');
        try {
            const res = await request(`/chat/update_model`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    mode_type: modeType,
                    mode_target_id: modeType === 'single' ? roleId : groupId,
                    conversation_id: conversationId,
                    model: mid,
                    event_type: event_type,
                    chat_channel: chat_channel
                })
            });
            if (res.error_code === 1003) {
                dialogAlert.show({
                    title: t('app.chat.switch_failed'),
                    desc: res.message,
                    alertStatus: 0
                })
            } else if (res.error_code === 0) {
                updateModelEventMap(event_type, mid, chat_channel, user?.chat_channel);
                auth?.updateUserInfo({
                    user_model: mid,
                    chat_channel: chat_channel
                });
                Toast.notify({ type: 'success', message: t('app.chat.switch_success') })
                // 用户主动切换了通道（尊享，共享的相互切换），或者免费额度用完，不再是新用户
                if (event_type === EventType.chat_man || event_type === EventType.chat_auto) {
                    setIsNewUser(false)
                }
            } else {
                Toast.notify({ type: 'error', message: res.message })
            }
        } catch (e: any) {
            console.error('changeModel error:', e);
            Toast.notify({ type: 'error', message: t('app.chat.switch_failed') })
        } finally {
            Toast.hideLoading();
            if (autoClose) {
                setIsEdit(false)
            }
        }
    };

    const recModel = useCallback(() => {
        // 如果用户当前的模型不在支持的模型中，需要推荐的模型
        if (!isCurModelInSupport && suggestModel) {
            if (payedUser) {
                changeModel({ mid: suggestModel?.mid, event_type: EventType.chat_auto })
                return;
            } else {
                // 如果存在免费的推荐模型，自动切换
                if (exitFreeRecModel) {
                    changeModel({ mid: suggestModel?.mid, event_type: EventType.chat_auto })
                    return;
                }
                // 如果不存在免费的推荐模型，提示用户充值
                confirm.show({
                    title: t('app.common.tips'),
                    desc: t('app.chat.switch_desc2', { not_pass_model: notPassModelNames?.join('、'), supported_model: models?.map((item: any) => item.model_name).join('、') }),
                    comfirmBtn: t('app.chat.btn_pay'),
                    cancelBtn: t('app.chat.switch_top_free_model', { model_name: suggestModel?.model_name }),
                    showIcon: false,
                    isCloseIconShow: false
                }).then((res) => {
                    if (res?.confirm) {
                        router.push(`/${lang}/pay`)
                    } else {
                        changeModel({ mid: suggestModel?.mid, event_type: EventType.chat_man })
                    }
                })
            }
        }
    }, []);

    // 如果用户chat_channel为空，说明是新用户，需要判断是否要弹切换模型弹窗
    // const canShowSwitchDialog = async () => {
    //     if (user?.chat_channel === '') {
    //         const res = await request('/user/chat/balance')
    //         if (res?.error_code === 0 && mid) {
    //             const benefits = res?.data?.benefits
    //             setChatBalance(benefits)
    //             // 如果当前模型的免费版还有次数，弹出切换模型弹窗
    //             const hasFreeModel = benefits[mid]?.sum_remain_times > 0
    //             if (hasFreeModel) {
    //                 const res = await confirm.show({
    //                     title: t('app.chat.switch_consume'),
    //                     desc: t('app.chat.switch_consume_desc'),
    //                     comfirmBtn: t('app.chat.switch_consume_comfirm'),
    //                     cancelBtn: t('app.chat.switch_consume_cancel'),
    //                     showIcon: false,
    //                     isCloseIconShow: false
    //                 })
    //                 changeModel({mid: mid, event_type: EventType.chat_man, chat_channel: res?.confirm ? 'PAID' : 'FREE_BENEFIT'})
    //             }
    //         }
    //     }
    // }
    const initNewUser = async () => {
        const allModels = await getChatBalance()
        switchToRecFreeModel(allModels)
    }
    // 处理模型切换的逻辑
    // 检查并处理模型切换
    useEffect(() => {
        // console.log('isNewUser', isNewUser)
        // 组件加载时检查是否显示切换模型弹窗
        const showSwitchModel = async () => {
            if (!isShowGuideSwitchModel) return
            const res = await request('/operation/popup?popup_type=pay_switch_model');
            const popup = res?.popup;
            if (popup?.type === 'pay_switch_model') {
                const userRes = await confirm.show({
                    title: popup.title,
                    icon: <LockOpenIcon className="h-6 w-6 text-purple-500" />,
                    desc: popup.content,
                    comfirmBtn: t('app.chat.switch'),
                    cancelBtn: t('app.chat.keep')
                })
                if (userRes.confirm) {
                    changeModel({ mid: popup.extra, event_type: EventType.chat_man })
                }
                setIsShowGuideSwitchModel(false)
                localStorage.setItem('showGuideSwitchModel', '1')
            }
        };
        const searchParam = new URLSearchParams(location.search)
        // 已经标记新用户，获取聊天余额，切换到推荐免费模型
        if (isNewUser) {
            initNewUser()
            // 推广进来的，直接走新用户逻辑
        } else if (searchParam.get('type') === 'deeplink') {
            checkNewUserFreeChat()
        } else if (isNewUser !== null) {
            getChatBalance()
            recModel();
            // canShowSwitchDialog();
            showSwitchModel();
        }
    }, [isNewUser]);

    const changeNotPassModel = ({ mid }: any, chat_channel: string) => {
        if (mid === user?.user_model) {
            Toast.notify({
                type: 'info',
                message: t('app.chat.same_model')
            })
            return
        }
        const targetModel = allModels?.find((item: any) => item.mid == mid)
        const recModel = isCurModelInSupport ? curModel : suggestModel;
        // 如果当前模型和目标模型相同，直接返回
        if (recModel?.mid === targetModel?.mid) {
            changeModel({ mid: recModel?.mid, event_type: EventType.chat_man })
            return;
        }
        confirm.show({
            title: t('app.common.tips'),
            desc: t('app.chat.switch_desc', { not_pass_model: notPassModelNames?.join('、'), supported_model: models?.map((item: any) => item.model_name).join('、') }),
            comfirmBtn: t('app.chat.switch_model', { model: recModel?.short_name }),
            cancelBtn: t('app.chat.continue_model', { model: targetModel?.short_name }),
            showIcon: false,
            isCloseIconShow: false
        }).then((res) => {
            if (res?.confirm) {
                changeModel({ mid: recModel?.mid, event_type: EventType.chat_man, chat_channel: chat_channel })
            } else {
                changeModel({ mid: mid, event_type: EventType.chat_man, chat_channel: chat_channel })
            }
        })
    }

    const isFreeBanlance = user?.chat_channel === 'FREE_BENEFIT';
    const showModels = async () => {
        Toast.showLoading('');
        const res = await request('/user/chat/balance');
        Toast.hideLoading();
        if (res.error_code === 0) {
            setChatBalance(res.data.benefits)
        }
        setIsEdit(true)
    }
    const toggleShare = async (eventType: string = EventType.chat_man) => {
        const to_chat_channel = isFreeBanlance ? 'PAID' : 'FREE_BENEFIT';
        changeModel({ mid: mid, event_type: eventType, chat_channel: to_chat_channel, autoClose: false })
    }

    // console.log('isNewUser', isNewUser)
    // 切换到推荐免费模型
    const switchToRecFreeModel = (_allModels: any = allModels) => {
        // 筛选出含免费额度的模型
        const freeModels = _allModels?.filter((item: any) => item.sum_remain_times > 0)
        // 默认选中免费模式里面含次数的最贵的模型
        const model = freeModels?.sort((a: any, b: any) => b.price - a.price)[0]
        // console.log('freeModels', freeModels, model)
        // 如果有免费模型，切换到免费模型，如果没有，切换到付费模型
        if (model) {
            changeModel({ mid: model.mid, event_type: EventType.chat_auto_free, chat_channel: 'FREE_BENEFIT' })
        } else {
            changeModel({ mid: user?.user_model, event_type: EventType.chat_auto, chat_channel: 'PAID' })
            setIsNewUser(false)
        }
    }
    // 获取聊天余额,并跳转到推荐免费模型
    const getChatBalance = () => {
        return new Promise(async (resolve) => {
        const res = await request('/user/chat/balance');
        const benefits = res?.data?.benefits;
        if (res.error_code === 0) {
            setChatBalance(benefits)
            const allModels = user?.chat_products?.map((item: any) => {
                return {
                    ...item,
                    sum_remain_times: benefits[item.mid]?.sum_remain_times,
                    sum_total_times: benefits[item.mid]?.sum_reward_times,
                }
            });
            // console.log('res', res?.data.payed_user);
            // 有可能用户支付成功后，但是没有更新payed_user，所以这里需要更新
            auth?.updateUserInfo({ payed_user: res?.data.payed_user })
            resolve(allModels)
        } else {
            resolve([])
        }
    })
    }
    // 检查新用户是否有免费聊天权益，如果有免费聊天权益，切换到推荐免费模型，并且设置新用户标识，开启新用户引导
    const checkNewUserFreeChat = async () => {
        const res = await request('/operation/popup?popup_type=new_user_benefit&popup_position=HOME');
        const popup = res?.popup;
        if (popup) {
            await dialogAlert.show({
                title: t('app.chat.benefit_title'),
                desc: t('app.chat.benefit_desc'),
                alertStatus: '2',
                comfirmBtn: t('app.chat.benefit_btn'),
                isCloseIconShow: false
            })
            Toast.notify({
                type: 'success',
                message: t('app.chat.benefit_claim_status')
            })
            setIsNewUser(true)
            setChatTimes(0)
        }
    }

    // 新用户充值提示
    const showNewUserRechargeTip = async () => {
        await dialogAlert.show({
            title: t('app.chat.new_user_recharge_tips'),
            desc: t('app.chat.new_user_recharge_tips_desc'),
            alertStatus: 2,
        })
        router.push(`/${lang}/pay`)
    }

    const [showSwitchModelTip, setShowSwitchModelTip] = useState(false)
    // 每次聊天当前免费额度次数-1
    useEffect(() => {
        // console.log('chatTimes', chatTimes, isNewUser)
        if (chatTimes > 0) {
            setChatBalance((n: any) => {
                if (mid && n[mid]) {
                    n[mid].sum_remain_times--
                }
                return n
            })
            if(isNewUser) {
                if (chatTimes === 5) {
                    setShowSwitchModelTip(true)
                    const hideSwitchModelTip = () => {
                        setShowSwitchModelTip(false)
                        document.body.removeEventListener('click', hideSwitchModelTip)
                    }
                    document.body.addEventListener('click', hideSwitchModelTip)
                    return () => {
                        document.body.removeEventListener('click', hideSwitchModelTip)
                    }
                }
                if (chatTimes === 20 && !user?.payed_user) {
                    showNewUserRechargeTip()
                }
            }
        }
    }, [chatTimes])

    useEffect(() => {
        // 非编辑界面，如果当前模型是免费模型，并且剩余次数为0，并且不是新用户，自动切换到付费模型
        if (!isEdit && isFreeBanlance && curModel?.sum_remain_times === 0 && !isNewUser) {
            toggleShare(EventType.chat_channel_auto)
        }
    }, [isEdit, curModel?.sum_remain_times])
    // 如果当前模型的免费版次数用完，切换到推荐免费模型模型
    // console.log('curModel', curModel)
    useEffect(() => {
        if (curModel?.sum_remain_times === 0) {
            if (isNewUser) {
                // console.log('curModel.sum_remain_times', curModel.sum_remain_times, isNewUser)
                switchToRecFreeModel()
            }
        }
    }, [curModel?.sum_remain_times])

    return <>
        <div className='flex items-center justify-center relative w-[72px] box-border'>
            <button type='button' className='flex items-center' onClick={showModels}>
                {isFreeBanlance ? <Image src={turtleSvg} width={18} height={18} className='w-5 h-5 ' alt='rocket' /> : <Image src={rocketSvg} width={18} height={18} className='w-5 h-5 ' alt='rocket' />}
                <div className='flex items-center text-sm'>{curModel?.icon}</div>
                <div className=''><ArrowsUpDownIcon width={18} height={18} className='w-5 h-5 text-gray-500 dark:text-white' /></div>
            </button>
            {
                showSwitchModelTip && (
                    <div className='rounded-lg border py-1 px-2 left-3 bottom-16 absolute w-32 text-xs z-10 bg-white dark:bg-[var(--background)]'>
                        {t('app.chat.switch_model_tip')}
                        <div
                            className="absolute left-6 -bottom-2 w-0 h-0 border-l-8 border-r-8 border-t-8 border-l-transparent border-r-transparent border-t-gray-300 dark:border-t-white border-t-solid"
                        />
                        <div
                            className="absolute left-6 -bottom-[7px] w-0 h-0 border-l-8 border-r-8 border-t-8 border-l-transparent border-r-transparent border-t-white dark:border-t-black border-t-solid"
                        />
                    </div>
                )
            }
        </div>
        {isEdit && createPortal(<Modal isOpen={true} onClose={() => { setIsEdit(false) }} className='w-full max-w-2xl'>
            <div className='px-1 pb-1 pt-1 flex flex-wrap gap-6 justify-center sm:justify-normal'>
                <div className=''>
                    <div className='flex justify-center items-center  rounded text-xs bg-gray-300 dark:bg-gray-800 dark:text-white py-1 mt-5'>
                        <span className='ml-2'>{t('app.chat.current')}</span>
                        <div onClick={() => { toggleShare() }} className='ml-1 flex items-center rounded'>
                            {isFreeBanlance ? <Image src={turtleSvg} width={20} height={20} className='w-6 h-6 ' alt='rocket' /> : <Image src={rocketSvg} width={20} height={20} className='w-6 h-6 ' alt='rocket' />}
                            <span className=''>{isFreeBanlance ? t('app.chat.net_slow') + t('app.chat.tab_share') : t('app.chat.net_fast') + t('app.chat.tab_pay')}</span>
                        </div>
                        <span className='ml-1'>{curModel?.short_name}</span>
                    </div>
                    <div className="text-sm overflow-y-auto max-h-[calc(100vh_-_100px)]">
                        {isNewUser && freeModels?.length > 0 && <FreeModelItems freeModels={freeModels} mid={mid} changeModel={changeModel} user={user} />}
                        <div className="mt-2 p-1 pt-12 px-1 rounded border border-gray-300 dark:border-gray-500 relative overflow-hidden">
                            <div className='text-xs absolute left-0 top-0 bg-gray-300 dark:bg-gray-800 dark:text-white p-1 border-b border-gray-300 dark:border-gray-500'>
                                {t('app.chat.vip_model_desc')}
                            </div>
                            {
                                notPassModels?.map((model: any) => {
                                    return <MenuItem key={model.mid} model={model} mid={mid} changeModel={changeNotPassModel} isFreeBanlance={false} chat_channel='PAID' cl='opacity-50' user={user} />
                                })
                            }
                            {
                                models?.map((model: any) => {
                                    return <MenuItem key={model.mid} model={model} mid={mid} changeModel={changeModel} isFreeBanlance={false} chat_channel='PAID' user={user} />
                                })
                            }
                        </div>

                        {!isNewUser && freeModels?.length > 0 &&  <FreeModelItems freeModels={freeModels} mid={mid} changeModel={changeModel} user={user} />}
                    </div>
                </div>
            </div>
        </Modal>, document.body)}
    </>
}

export default SwitchModel;

const FreeModelItems = ({ freeModels, mid, changeModel, user }: any) => {
    const { t } = useTranslation()
    return <div className="mt-2 p-1 pt-12 px-1 rounded border border border-gray-300 dark:border-gray-500 relative overflow-hidden">
        <div className='text-xs absolute left-0 top-0 bg-gray-300 dark:bg-gray-800 dark:text-white p-1 border-b border-gray-300 dark:border-gray-500'>
            {t('app.chat.share_model_desc')}
        </div>
        {
            freeModels?.map((model: any) => {
                return <MenuItem key={model.mid} model={model} mid={mid} changeModel={changeModel} isFreeBanlance={true} chat_channel='FREE_BENEFIT' user={user} />
            })
        }
    </div>
}

const MenuItem = ({ model, mid, changeModel, cl, user, isFreeBanlance, chat_channel }: any) => {
    const { t } = useTranslation()
    return <div
        onClick={() => { changeModel({ mid: model.mid, event_type: EventType.chat_man, chat_channel }) }}
        className={`px-1.5 hover:bg-violet-500 hover:text-white group w-full items-center rounded-md py-2 text-sm dark:text-zinc-100 flex gap-2 ${cl} cursor-pointer`}
    >
        <div className='flex-1'>
            <p>{model.model_name}
                {isFreeBanlance ? <>
                    <span className='ml-2 text-xs text-yellow-600'>[{t('app.chat.remain')}：{model.sum_remain_times}/{model.sum_total_times}] [{model.sum_remain_times == 0 ? t('app.chat.net_off') : t('app.chat.net_slow')}]</span>
                </> : <span className='ml-2 text-xs text-yellow-600'>{model.price} {t('app.chat.price')}</span>
                }
            </p>
            <p className={`text-xs text-gray-500 group-hover:text-white`}>{model.desc}</p>
        </div>
        {model.mid == mid && user?.chat_channel === chat_channel && <CheckCircleIcon className='w-4 h-4 ' />}
    </div>
}