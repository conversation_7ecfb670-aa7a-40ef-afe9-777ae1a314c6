// app/components/ui/Inventory.tsx
"use client";
import React from 'react';
import cn from 'classnames';
import { useTranslation } from 'react-i18next'
import ReactMarkdown from 'react-markdown';
import Toast from '@little-tavern/shared/src/ui/toast';

export type InventoryType = {
    amount: string;
    total_amount: string;
    display_fee: string;
    desc: string;
    recharge_product_id: string;
    corner_tip: string
    title: string
    display_price: string
    corner_title: string
    promotion_price_desc: string
    original_price_desc: string
    promotion_desc: string
    remarks: string
    button_text: string
    feature_list: string[],
    price: number
    reward_amount: number
    supported_pay_types: string[]
};

type InventoryProps = {
    inventorys: InventoryType[];
    select: (index: number) => void,
    selectedInventoryIndex: number
    rechargeData: any
};
const Inventory = ({ inventorys, select, selectedInventoryIndex, rechargeData }: InventoryProps) => {
    const { t } = useTranslation()
    const onSelect = (index: number, isDisabled: boolean, disabledMsg: string) => {
        if(isDisabled) {
            Toast.notify({type: 'error', message: disabledMsg})
            return;
        }
        select(index)
    }
    return (<div className='pt-1 md:pb-4 '>
        {rechargeData?.banner_tip && <div className='text-center'><div className='bg-red-500 rounded p-0.5 text-white inline-block mx-auto text-sm mb-2.5 px-3'>{rechargeData?.banner_tip}</div></div>}
        <div className="grid grid-cols-2 sm:grid-cols-3 lg:grid-cols-4 gap-2">
            {
                inventorys?.length > 0 && inventorys?.map((inventory: any, index: number) => {
                    return <div key={inventory.recharge_product_id} className={cn('w-full rounded-md pt-1 pb-2 px-1 border dark:border-gray-500 dark:bg-gray-900 bg-white relative cursor-pointer', selectedInventoryIndex === index && 'border-1 !border-purple-500 dark:!bg-gray-800', inventory.disabled && 'opacity-50')} onClick={() => onSelect(index, inventory.disabled, inventory.disabled_message)}>
                        {(inventory.corner_title || inventory.corner_tip) && <div className='absolute flex flex-col items-end -right-[1px] -top-[1px] text-right'>
                            {inventory.corner_title && <div className='text-xs bg-red-500 rounded p-0.5 text-white'>{inventory.corner_title}</div>}
                            <p className='text-xs text-yellow-500 mr-0.5'>{inventory.corner_tip}</p>
                        </div>}

                        <div className='text-sm dark:text-gray-200'>{inventory.title}</div>
                        <div className='text-center mt-3'>
                            <div className="mt-1 mb-2">
                                <div className=''>
                                    <div className="desc text-lg text-purple-500 mt-2 dark:font-bold">{inventory.display_price}</div>
                                    {inventory.total_amount !== 0 && <div className="name text-2xl dark:font-bold">{inventory.total_amount}<span className='text-base inline-block mx-1'>{inventory?.reward_amount ? '💎+🟡' : '💎'}</span></div>}
                                </div>
                            </div>
                            {
                                inventory.promotion_desc ? <div className='text-yellow-500 text-xs'>{inventory.promotion_desc}</div> : <div className='text-yellow-500 text-xs'>{inventory.desc}</div>
                            }
                        </div>
                    </div>
                })
            }
        </div>
        <div className='text-xs dark:text-gray-100 mt-1 text-left px-1 mt-2'>
            {rechargeData?.remarks?.length > 0 && rechargeData?.remarks?.map((remark: string, index: number) => {
                return <div key={index} className=''><ReactMarkdown components={{
                    li: ({node, ...props}) => <li className='list-decimal ml-4' {...props} />,
                              a: ({node, ...props}) => <a target="_blank" className='text-blue-500 underline' rel="" {...props} />
                            }}>{remark}</ReactMarkdown></div>
            })}
        </div>
    </div>
    );
};

export default Inventory;
