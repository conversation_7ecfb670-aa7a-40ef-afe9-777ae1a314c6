import { useEffect, useState, useCallback } from 'react';
import Toast from '@share/src/ui/toast';
import useRequest from '../hook/useRequest';

type PaymentConfigItem = {
  id: number;
  channel: string;
  pay_type: string;
  min_amount: number;
  max_amount: number;
  ratio: number;
  remark: string;
  enabled: boolean; // 添加是否启用字段
  isEditing?: boolean;
};

type WhitelistItem = {
  tg_id: number;
  channel: string;
  enabled: boolean;
};

type WhitelistByChannel = {
  [channel: string]: WhitelistItem[];
};

const PaymentConfig = () => {
  const request = useRequest();
  const [configList, setConfigList] = useState<PaymentConfigItem[]>([]);
  const [loading, setLoading] = useState(false);

  // 批量编辑相关状态
  const [editingItems, setEditingItems] = useState<Set<number>>(new Set()); // 正在编辑的项目ID
  const [changedItems, setChangedItems] = useState<Map<number, PaymentConfigItem>>(new Map()); // 已修改的项目
  const [newItems, setNewItems] = useState<PaymentConfigItem[]>([]); // 新增的项目
  const [batchSubmitting, setBatchSubmitting] = useState(false); // 批量提交状态
  
  // 排序相关状态
  const [sortConfig, setSortConfig] = useState<{
    key: 'channel' | 'pay_type' | null;
    direction: 'asc' | 'desc';
  }>({ key: null, direction: 'asc' });

  // 白名单相关状态
  const [whitelistData, setWhitelistData] = useState<WhitelistByChannel>({});
  const [whitelistLoading, setWhitelistLoading] = useState(false);
  const [newWhitelistItem, setNewWhitelistItem] = useState<{ tg_id: string; channel: string }>({ tg_id: '', channel: '' });
  const [showAddWhitelist, setShowAddWhitelist] = useState(false);
  const [activeTab, setActiveTab] = useState<'payment' | 'whitelist'>('payment');
  const [channels, setChannels] = useState<string[]>([]);
  const [channelsLoading, setChannelsLoading] = useState(false);

  // 获取配置列表
  const getConfigList = useCallback(async () => {
    setLoading(true);
    try {
      const res = await request(`/channel_control/list`);
      setConfigList(res.controls);
    } catch (e) {
      Toast.notify({
        type: 'error',
        message: '获取支付配置失败'
      });
    } finally {
      setLoading(false);
    }
  }, [request]);

  // 获取白名单列表
  const getWhitelistData = useCallback(async () => {
    setWhitelistLoading(true);
    try {
      const res = await request(`/channel_control/whitelist`);
      const whitelist = res.whitelist;

      // 按 channel 分组
      const groupedData: WhitelistByChannel = {};
      whitelist.forEach((item: WhitelistItem) => {
        if (!item.enabled) return;

        if (!groupedData[item.channel]) {
          groupedData[item.channel] = [];
        }
        groupedData[item.channel].push(item);
      });

      setWhitelistData(groupedData);
    } catch (e) {
      Toast.notify({
        type: 'error',
        message: '获取白名单失败'
      });
    } finally {
      setWhitelistLoading(false);
    }
  }, [request]);

  // 获取渠道列表
  const getChannels = useCallback(async () => {
    setChannelsLoading(true);
    try {
      const res = await request(`/channel_control/channels`);
      setChannels(res.channels);
    } catch (e) {
      Toast.notify({
        type: 'error',
        message: '获取渠道列表失败'
      });
    } finally {
      setChannelsLoading(false);
    }
  }, [request]);

  // 添加白名单
  const handleAddWhitelist = async () => {
    if (!newWhitelistItem.tg_id || !newWhitelistItem.channel) {
      Toast.notify({
        type: 'error',
        message: '请填写完整信息'
      });
      return;
    }

    try {
      await request(`/channel_control/whitelist/add`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          tg_id: parseInt(newWhitelistItem.tg_id),
          channel: newWhitelistItem.channel
        })
      });

      Toast.notify({
        type: 'success',
        message: '添加成功'
      });

      setNewWhitelistItem({ tg_id: '', channel: '' });
      setShowAddWhitelist(false);
      getWhitelistData();
    } catch (e) {
      Toast.notify({
        type: 'error',
        message: '添加失败'
      });
    }
  };

  // 删除白名单
  const handleDeleteWhitelist = async (tg_id: number, channel: string) => {
    if (!confirm('确定要删除这个白名单项吗？')) {
      return;
    }

    try {
      await request(`/channel_control/whitelist/delete/${tg_id}/${channel}`, {
        method: 'POST'
      });

      Toast.notify({
        type: 'success',
        message: '删除成功'
      });

      getWhitelistData();
    } catch (e) {
      Toast.notify({
        type: 'error',
        message: '删除失败'
      });
    }
  };

  // 启用白名单（调用 add 接口）
  const handleEnableWhitelist = async (tg_id: number, channel: string) => {
    try {
      await request(`/channel_control/whitelist/add`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          tg_id: tg_id,
          channel: channel
        })
      });

      Toast.notify({
        type: 'success',
        message: '启用成功'
      });

      getWhitelistData();
    } catch (e) {
      Toast.notify({
        type: 'error',
        message: '启用失败'
      });
    }
  };

  // 添加新项目
  const handleAddItem = () => {
    const newId = Date.now(); // 使用时间戳作为临时ID
    const newItemData: PaymentConfigItem = {
      id: newId,
      channel: '',
      pay_type: '',
      min_amount: 0,
      max_amount: 0,
      ratio: 100,
      remark: '',
      enabled: true,
      isEditing: true
    };

    setNewItems(prev => [...prev, newItemData]);
    setEditingItems(prev => {
      const newSet = new Set(prev);
      newSet.add(newId);
      return newSet;
    });
  };

  // 批量提交所有变更
  const handleBatchSubmit = async () => {
    if (newItems.length === 0 && changedItems.size === 0) {
      Toast.notify({
        type: 'warning',
        message: '没有需要提交的变更'
      });
      return;
    }

    setBatchSubmitting(true);

    try {
      const items: Array<{
        id: number | null;
        channel: string;
        pay_type: string;
        min_amount: number;
        max_amount: number;
        enabled: boolean;
        ratio: number;
        remark: string;
      }> = [];

      // 添加新增项目
      newItems.forEach(item => {
        items.push({
          id: null, // 新增项目ID为null
          channel: item.channel,
          pay_type: item.pay_type,
          min_amount: item.min_amount,
          max_amount: item.max_amount,
          enabled: item.enabled,
          ratio: item.ratio,
          remark: item.remark
        });
      });

      // 添加修改项目
      changedItems.forEach((item, id) => {
        items.push({
          id: id,
          channel: item.channel,
          pay_type: item.pay_type,
          min_amount: item.min_amount,
          max_amount: item.max_amount,
          enabled: item.enabled,
          ratio: item.ratio,
          remark: item.remark
        });
      });

      // 调用批量更新接口
      await request(`/channel_control/batch`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ items })
      });

      Toast.notify({
        type: 'success',
        message: '批量提交成功'
      });

      // 清空编辑状态
      setNewItems([]);
      setChangedItems(new Map());
      setEditingItems(new Set());

      // 重新获取数据
      getConfigList();

    } catch (e) {
      Toast.notify({
        type: 'error',
        message: '批量提交失败'
      });
    } finally {
      setBatchSubmitting(false);
    }
  };

  // 重置所有变更
  const handleResetAll = () => {
    if (newItems.length === 0 && changedItems.size === 0) {
      return;
    }

    if (!confirm('确定要重置所有未提交的变更吗？此操作不可撤销。')) {
      return;
    }

    // 重置所有编辑状态
    setConfigList(configList.map(item => ({ ...item, isEditing: false })));
    setNewItems([]);
    setChangedItems(new Map());
    setEditingItems(new Set());

    Toast.notify({
      type: 'success',
      message: '已重置所有变更'
    });
  };

  // 取消编辑
  const handleCancelEdit = (id: number) => {
    // 检查是否是新增项目
    const isNewItem = newItems.some(item => item.id === id);

    if (isNewItem) {
      // 移除新增项目
      setNewItems(prev => prev.filter(item => item.id !== id));
    } else {
      // 恢复原始数据
      setConfigList(configList.map(item =>
        item.id === id ? { ...item, isEditing: false } : item
      ));
      // 移除变更记录
      setChangedItems(prev => {
        const newMap = new Map(prev);
        newMap.delete(id);
        return newMap;
      });
    }

    // 移除编辑状态
    setEditingItems(prev => {
      const newSet = new Set(prev);
      newSet.delete(id);
      return newSet;
    });
  };

  // 开始编辑
  const handleStartEdit = (id: number) => {
    setConfigList(configList.map(item =>
      item.id === id ? { ...item, isEditing: true } : item
    ));
    setEditingItems(prev => {
      const newSet = new Set(prev);
      newSet.add(id);
      return newSet;
    });
  };

  // 处理输入变化
  const handleInputChange = (id: number, field: keyof PaymentConfigItem, value: string | number | boolean) => {
    // 检查是否是新增项目
    const isNewItem = newItems.some(item => item.id === id);

    if (isNewItem) {
      // 更新新增项目
      setNewItems(prev => prev.map(item =>
        item.id === id ? { ...item, [field]: value } : item
      ));
    } else {
      // 更新现有项目
      setConfigList(configList.map(item =>
        item.id === id ? { ...item, [field]: value } : item
      ));

      // 记录变更
      const updatedItem = configList.find(item => item.id === id);
      if (updatedItem) {
        setChangedItems(prev => {
          const newMap = new Map(prev);
          newMap.set(id, { ...updatedItem, [field]: value });
          return newMap;
        });
      }
    }
  };

  // 排序函数
  const handleSort = (key: 'channel' | 'pay_type') => {
    let direction: 'asc' | 'desc' = 'asc';
    if (sortConfig.key === key && sortConfig.direction === 'asc') {
      direction = 'desc';
    }
    
    setSortConfig({ key, direction });
    
    const sortedList = [...configList].sort((a, b) => {
      if (a[key] < b[key]) {
        return direction === 'asc' ? -1 : 1;
      }
      if (a[key] > b[key]) {
        return direction === 'asc' ? 1 : -1;
      }
      return 0;
    });
    
    setConfigList(sortedList);
  };

  // 获取排序图标
  const getSortIcon = (key: 'channel' | 'pay_type') => {
    if (sortConfig.key !== key) {
      return <span className="ml-1 text-gray-500">↕️</span>;
    }
    return sortConfig.direction === 'asc' ? 
      <span className="ml-1 text-blue-400">↑</span> : 
      <span className="ml-1 text-blue-400">↓</span>;
  };

  useEffect(() => {
    getConfigList();
    getWhitelistData();
    getChannels();
  }, [getConfigList, getWhitelistData, getChannels]);

  // 渲染表格行
  const renderTableRow = (item: PaymentConfigItem) => {
    const isEditing = item.isEditing;
    const isNewItem = newItems.some(newItem => newItem.id === item.id);
    const isChanged = changedItems.has(item.id);
    
    return (
      <tr key={item.id} className={`ml-3 my-1 border-1 text-gray-400 ${
        isNewItem ? 'bg-blue-900/20 border-blue-500' :
        isChanged ? 'bg-yellow-900/20 border-yellow-500' :
        isEditing ? 'bg-gray-800' : ''
      }`}>
        <td className="border border-slate-700 py-1 px-1">
          {isEditing ? (
            isNewItem ? (
              <select
                className="bg-gray-800 w-full px-2 py-1"
                value={item.channel}
                onChange={(e) => handleInputChange(item.id, 'channel', e.target.value)}
              >
                <option value="">请选择渠道</option>
                {channels.map((channel) => (
                  <option key={channel} value={channel}>
                    {channel}
                  </option>
                ))}
              </select>
            ) : (
              <input
                type="text"
                className="bg-gray-800 w-full px-2 py-1"
                value={item.channel}
                disabled={true} // 现有项目不可编辑渠道
                onChange={(e) => handleInputChange(item.id, 'channel', e.target.value)}
              />
            )
          ) : (
            item.channel
          )}
        </td>
        <td className="border border-slate-700 py-1 px-1">
          {isEditing ? (
            isNewItem ? (
              <select
                className="bg-gray-800 w-full px-2 py-1"
                value={item.pay_type}
                onChange={(e) => handleInputChange(item.id, 'pay_type', e.target.value)}
              >
                <option value="alipay">alipay</option>
                <option value="wechat">wechat</option>
              </select>
            ) : (
              <input
                type="text"
                className="bg-gray-800 w-full px-2 py-1"
                value={item.pay_type}
                disabled={true} // 现有项目不可编辑支付类型
                onChange={(e) => handleInputChange(item.id, 'pay_type', e.target.value)}
              />
            )
          ) : (
            item.pay_type
          )}
        </td>
        <td className="border border-slate-700 py-1 px-1">
          {isEditing ? (
            <input
              type="number"
              className="bg-gray-800 w-full px-2 py-1"
              value={item.ratio}
              onChange={(e) => handleInputChange(item.id, 'ratio', parseInt(e.target.value) || 0)}
            />
          ) : (
            item.ratio
          )}
        </td>
        <td className="border border-slate-700 py-1 px-1">
          {isEditing ? (
            <input
              type="number"
              className="bg-gray-800 w-full px-2 py-1"
              value={item.min_amount}
              onChange={(e) => handleInputChange(item.id, 'min_amount', parseInt(e.target.value) || 0)}
            />
          ) : (
            item.min_amount
          )}
        </td>
        <td className="border border-slate-700 py-1 px-1">
          {isEditing ? (
            <input
              type="number"
              className="bg-gray-800 w-full px-2 py-1"
              value={item.max_amount}
              onChange={(e) => handleInputChange(item.id, 'max_amount', parseInt(e.target.value) || 0)}
            />
          ) : (
            item.max_amount
          )}
        </td>
        <td className="border border-slate-700 py-1 px-1 text-center">
          {isEditing ? (
            <input
              type="checkbox"
              className="h-4 w-4"
              checked={item.enabled}
              onChange={(e) => handleInputChange(item.id, 'enabled', e.target.checked)}
            />
          ) : (
            <div className="flex justify-center">
              <span 
                className={`inline-block w-3 h-3 rounded-full ${item.enabled ? 'bg-green-500' : 'bg-red-500'}`}
              ></span>
            </div>
          )}
        </td>
        <td className="border border-slate-700 py-1 px-1">
          {isEditing ? (
            <input
              type="text"
              className="bg-gray-800 w-full px-2 py-1"
              value={item.remark}
              onChange={(e) => handleInputChange(item.id, 'remark', e.target.value)}
            />
          ) : (
            item.remark
          )}
        </td>
        <td className="border border-slate-700 py-1 px-1">
          <div className="flex flex-wrap gap-1">
            {isEditing ? (
              <button
                type="button"
                className="p-1 px-3 bg-gray-500 text-white rounded text-sm"
                onClick={() => handleCancelEdit(item.id)}
              >
                取消
              </button>
            ) : (
              <button
                type="button"
                className="p-1 px-3 bg-sky-500 text-white rounded text-sm"
                onClick={() => handleStartEdit(item.id)}
              >
                编辑
              </button>
            )}
            {(isNewItem || isChanged) && (
              <span className="text-xs text-yellow-400 ml-1">
                {isNewItem ? '新增' : '已修改'}
              </span>
            )}
          </div>
        </td>
      </tr>
    );
  };

  // 渲染白名单添加表单
  const renderAddWhitelistForm = () => (
    <div className="mb-4 p-4 border border-slate-600 rounded bg-gray-800">
      <h3 className="text-white mb-3">添加白名单</h3>
      <div className="flex gap-3 items-end">
        <div>
          <label className="block text-gray-300 text-sm mb-1">TG ID</label>
          <input
            type="number"
            className="bg-gray-700 text-white px-3 py-2 rounded border border-slate-600"
            value={newWhitelistItem.tg_id}
            onChange={(e) => setNewWhitelistItem({ ...newWhitelistItem, tg_id: e.target.value })}
            placeholder="输入 TG ID"
          />
        </div>
        <div>
          <label className="block text-gray-300 text-sm mb-1">渠道</label>
          <select
            className="bg-gray-700 text-white px-3 py-2 rounded border border-slate-600"
            value={newWhitelistItem.channel}
            onChange={(e) => setNewWhitelistItem({ ...newWhitelistItem, channel: e.target.value })}
          >
            <option value="">请选择渠道</option>
            {channelsLoading ? (
              <option value="" disabled>加载中...</option>
            ) : (
              channels.map((channel) => (
                <option key={channel} value={channel}>
                  {channel}
                </option>
              ))
            )}
          </select>
        </div>
        <div className="flex gap-2">
          <button
            type="button"
            className="px-4 py-2 bg-green-500 text-white rounded text-sm"
            onClick={handleAddWhitelist}
          >
            添加
          </button>
          <button
            type="button"
            className="px-4 py-2 bg-gray-500 text-white rounded text-sm"
            onClick={() => setShowAddWhitelist(false)}
          >
            取消
          </button>
        </div>
      </div>
    </div>
  );

  // 渲染白名单表格
  const renderWhitelistTable = () => (
    <div className="mt-4">
      <div className="flex justify-between items-center mb-3">
        <h2 className="text-white text-lg">白名单管理</h2>
        <button
          type="button"
          className="p-2 px-5 bg-blue-500 text-white rounded text-sm"
          onClick={() => setShowAddWhitelist(true)}
        >
          添加白名单
        </button>
      </div>

      {showAddWhitelist && renderAddWhitelistForm()}

      {whitelistLoading ? (
        <div className="text-center text-gray-400 py-4">加载中...</div>
      ) : (
        <div className="space-y-6">
          {Object.keys(whitelistData).length === 0 ? (
            <div className="text-center text-gray-400 py-4">暂无白名单数据</div>
          ) : (
            Object.entries(whitelistData).map(([channel, items]) => (
              <div key={channel} className="border border-slate-600 rounded">
                <div className="bg-slate-700 px-4 py-2 text-white font-medium">
                  渠道: {channel} ({items.length} 个白名单)
                </div>
                <table className="w-full text-gray-400 text-sm">
                  <thead className="bg-slate-800">
                    <tr>
                      <th className="border border-slate-600 py-2 px-4 text-left">TG ID</th>
                      <th className="border border-slate-600 py-2 px-4 text-center">状态</th>
                      <th className="border border-slate-600 py-2 px-4 text-center">操作</th>
                    </tr>
                  </thead>
                  <tbody>
                    {items.map((item) => (
                      <tr key={`${item.tg_id}-${item.channel}`} className="hover:bg-slate-800">
                        <td className="border border-slate-600 py-2 px-4">{item.tg_id}</td>
                        <td className="border border-slate-600 py-2 px-4 text-center">
                          <span
                            className={`inline-block w-3 h-3 rounded-full ${item.enabled ? 'bg-green-500' : 'bg-red-500'}`}
                          ></span>
                        </td>
                        <td className="border border-slate-600 py-2 px-4 text-center">
                          <div className="flex gap-2 justify-center">
                            {!item.enabled && (
                              <button
                                type="button"
                                className="px-3 py-1 bg-green-500 text-white rounded text-sm"
                                onClick={() => handleEnableWhitelist(item.tg_id, item.channel)}
                              >
                                启用
                              </button>
                            )}
                            <button
                              type="button"
                              className="px-3 py-1 bg-red-500 text-white rounded text-sm"
                              onClick={() => handleDeleteWhitelist(item.tg_id, item.channel)}
                            >
                              删除
                            </button>
                          </div>
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            ))
          )}
        </div>
      )}
    </div>
  );

  return (
    <div className="mt-2">
      {/* Tab 切换 */}
      <div className="flex mb-4 border-b border-slate-600">
        <button
          type="button"
          className={`px-4 py-2 text-sm font-medium ${
            activeTab === 'payment'
              ? 'text-blue-400 border-b-2 border-blue-400'
              : 'text-gray-400 hover:text-white'
          }`}
          onClick={() => setActiveTab('payment')}
        >
          支付配置
        </button>
        <button
          type="button"
          className={`px-4 py-2 text-sm font-medium ${
            activeTab === 'whitelist'
              ? 'text-blue-400 border-b-2 border-blue-400'
              : 'text-gray-400 hover:text-white'
          }`}
          onClick={() => setActiveTab('whitelist')}
        >
          白名单管理
        </button>
      </div>

      {/* 支付配置内容 */}
      {activeTab === 'payment' && (
        <div>
          <div className="flex gap-3 mb-3">
            <button
              type="button"
              className="p-2 px-5 bg-purple-500 text-white rounded text-sm"
              onClick={handleAddItem}
            >
              新增配置
            </button>

            <button
              type="button"
              className={`p-2 px-5 text-white rounded text-sm ${
                (newItems.length > 0 || changedItems.size > 0) && !batchSubmitting
                  ? 'bg-green-500 hover:bg-green-600'
                  : 'bg-gray-500 cursor-not-allowed'
              }`}
              onClick={handleBatchSubmit}
              disabled={batchSubmitting || (newItems.length === 0 && changedItems.size === 0)}
            >
              {batchSubmitting ? '提交中...' : `批量提交 (${newItems.length + changedItems.size})`}
            </button>

            {(newItems.length > 0 || changedItems.size > 0) && (
              <button
                type="button"
                className="p-2 px-5 bg-red-500 hover:bg-red-600 text-white rounded text-sm"
                onClick={handleResetAll}
                disabled={batchSubmitting}
              >
                重置变更
              </button>
            )}

            {(newItems.length > 0 || changedItems.size > 0) && (
              <div className="flex items-center text-yellow-400 text-sm">
                <span>有未提交的变更</span>
              </div>
            )}
          </div>

          <table className="dark:bg-gray-900 bg-white text-gray-500 text-sm border-collapse mt-2 w-full">
            <thead className="dark:bg-slate-700 bg-slate-300 dark:text-slate-300">
              <tr className="text-center text-xs sm:text-sm">
                <th className="border py-2 px-1 border-slate-600">
                  <button
                    type="button"
                    className="flex items-center justify-center w-full hover:text-blue-400 transition-colors"
                    onClick={() => handleSort('channel')}
                  >
                    渠道
                    {getSortIcon('channel')}
                  </button>
                </th>
                <th className="border py-2 px-4 border-slate-600">
                  <button
                    type="button"
                    className="flex items-center justify-center w-full hover:text-blue-400 transition-colors"
                    onClick={() => handleSort('pay_type')}
                  >
                    付款方式
                    {getSortIcon('pay_type')}
                  </button>
                </th>
                <th className="border py-2 px-4 border-slate-600">比例</th>
                <th className="border py-2 px-4 border-slate-600">最小金额</th>
                <th className="border py-2 px-4 border-slate-600">最大金额</th>
                <th className="border py-2 px-4 border-slate-600">是否启用</th>
                <th className="border py-2 px-4 border-slate-600">备注</th>
                <th className="border py-2 px-3 border-slate-600">操作</th>
              </tr>
            </thead>
            <tbody>
              {configList.map(item => renderTableRow(item))}
              {newItems.map(item => renderTableRow(item))}
            </tbody>
          </table>
        </div>
      )}

      {/* 白名单管理内容 */}
      {activeTab === 'whitelist' && renderWhitelistTable()}
    </div>
  );
};

export default PaymentConfig;
